{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "from pydantic import BaseModel, ValidationError, Field\n", "\n", "\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion, OpenAIChatPromptExecutionSettings\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "\n", "from semantic_kernel.functions import KernelArguments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "client = AsyncOpenAI(\n", "    api_key=os.environ.get(\"GITHUB_TOKEN\"), \n", "    base_url=\"https://models.inference.ai.azure.com/\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SubTask(BaseModel):\n", "    assigned_agent: str = Field(\n", "        description=\"The specific agent assigned to handle this subtask\")\n", "    task_details: str = Field(\n", "        description=\"Detailed description of what needs to be done for this subtask\")\n", "\n", "\n", "class TravelPlan(BaseModel):\n", "    main_task: str = Field(\n", "        description=\"The overall travel request from the user\")\n", "    subtasks: List[SubTask] = Field(\n", "        description=\"List of subtasks broken down from the main task, each assigned to a specialized agent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AGENT_NAME = \"TravelAgent\"\n", "AGENT_INSTRUCTIONS = \"\"\"You are an planner agent.\n", "    Your job is to decide which agents to run based on the user's request.\n", "    Below are the available agents specialised in different tasks:\n", "    - FlightBooking: For booking flights and providing flight information\n", "    - HotelBooking: For booking hotels and providing hotel information\n", "    - CarRental: For booking cars and providing car rental information\n", "    - ActivitiesBooking: For booking activities and providing activity information\n", "    - DestinationInfo: For providing information about destinations\n", "    - DefaultAgent: For handling general requests\"\"\"\n", "\n", "# Create the prompt execution settings and configure the Pydantic model response format\n", "settings = OpenAIChatPromptExecutionSettings(response_format=TravelPlan)\n", "\n", "agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    name=AGENT_NAME,\n", "    instructions=AGENT_INSTRUCTIONS,\n", "    arguments=KernelArguments(settings) \n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "\n", "\n", "async def main():\n", "    # Create a thread for the agent\n", "    # If no thread is provided, a new thread will be\n", "    # created and returned with the initial response\n", "    thread: ChatHistoryAgentThread | None = None\n", "\n", "    # Respond to user input\n", "    user_inputs = [\n", "        \"Create a travel plan for a family of 4, with 2 kids, from Singapore to Melbourne\",\n", "    ]\n", "\n", "    for user_input in user_inputs:\n", "        \n", "        # Start building HTML output\n", "        html_output = \"<div style='margin-bottom:10px'>\"\n", "        html_output += \"<div style='font-weight:bold'>User:</div>\"\n", "        html_output += f\"<div style='margin-left:20px'>{user_input}</div>\"\n", "        html_output += \"</div>\"\n", "\n", "        # Collect the agent's response\n", "        response = await agent.get_response(messages=user_input, thread=thread)\n", "        thread = response.thread\n", "\n", "        try:\n", "            # Try to validate the response as a TravelPlan\n", "            travel_plan = TravelPlan.model_validate(json.loads(response.message.content))\n", "\n", "            # Display the validated model as formatted JSON\n", "            formatted_json = travel_plan.model_dump_json(indent=4)\n", "            html_output += \"<div style='margin-bottom:20px'>\"\n", "            html_output += \"<div style='font-weight:bold'>Validated Travel Plan:</div>\"\n", "            html_output += f\"<pre style='margin-left:20px; padding:10px; border-radius:5px;'>{formatted_json}</pre>\"\n", "            html_output += \"</div>\"\n", "        except ValidationError as e:\n", "            # Handle validation errors\n", "            html_output += \"<div style='margin-bottom:20px; color:red;'>\"\n", "            html_output += \"<div style='font-weight:bold'>Validation Error:</div>\"\n", "            html_output += f\"<pre style='margin-left:20px;'>{str(e)}</pre>\"\n", "            html_output += \"</div>\"\n", "            # Add this to see what the response contains for debugging\n", "            html_output += \"<div style='margin-bottom:20px;'>\"\n", "            html_output += \"<div style='font-weight:bold'>Raw Response:</div>\"\n", "            html_output += f\"<div style='margin-left:20px; white-space:pre-wrap'>{response.content}</div>\"\n", "            html_output += \"</div>\"\n", "\n", "        html_output += \"<hr>\"\n", "\n", "        # Display formatted HTML\n", "        display(HTML(html_output))\n", "\n", "await main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You should see sample output similar to:\n", "\n", "```json\n", "User:\n", "Create a travel plan for a family of 4, with 2 kids, from Singapore to Melboune\n", "Validated Travel Plan:\n", "{\n", "    \"main_task\": \"Plan a family trip from Singapore to Melbourne for 4 people including 2 kids.\",\n", "    \"subtasks\": [\n", "        {\n", "            \"assigned_agent\": \"FlightBooking\",\n", "            \"task_details\": \"Book round-trip flights from Singapore to Melbourne for 2 adults and 2 children.\"\n", "        },\n", "        {\n", "            \"assigned_agent\": \"HotelBooking\",\n", "            \"task_details\": \"Find and book a family-friendly hotel in Melbourne that accommodates 4 people.\"\n", "        },\n", "        {\n", "            \"assigned_agent\": \"CarRental\",\n", "            \"task_details\": \"Arrange for a car rental in Melbourne suitable for a family of 4.\"\n", "        },\n", "        {\n", "            \"assigned_agent\": \"ActivitiesBooking\",\n", "            \"task_details\": \"Plan and book family-friendly activities in Melbourne suitable for kids.\"\n", "        },\n", "        {\n", "            \"assigned_agent\": \"DestinationInfo\",\n", "            \"task_details\": \"Provide information about Melbourne, including attractions, dining options, and family-oriented activities.\"\n", "        }\n", "    ]\n", "}\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}