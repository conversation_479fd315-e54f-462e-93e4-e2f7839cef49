{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Expense Claim Analysis\n", "\n", "This notebook demonstrates how to create agents that use plugins to process travel expenses from local receipt images, generate an expense claim email, and visualize expense data using a pie chart. Agents dynamically choose functions based on the task context.\n", "\n", "Steps:\n", "1. OCR Agent processes the local receipt image and extracts travel expense data.\n", "2. Email Agent generates an expense claim email.\n", "\n", "### Example of a travel expense scenario:\n", "Imagine you're an employee traveling for a business meeting in another city. Your company has a policy to reimburse all reasonable travel-related expenses. Here’s a breakdown of potential travel expenses:\n", "- Transportation:\n", "Airfare for a round trip from your home city to the destination city.\n", "Taxi or ride-hailing services to and from the airport.\n", "Local transportation in the destination city (like public transit, rental cars, or taxis).\n", "\n", "- Accommodation:\n", "Hotel stay for three nights at a mid-range business hotel close to the meeting venue.\n", "\n", "- Meals:\n", "Daily meal allowance for breakfast, lunch, and dinner, based on the company's per diem policy.\n", "\n", "- Miscellaneous Expenses:\n", "Parking fees at the airport.\n", "Internet access charges at the hotel.\n", "Tips or small service charges.\n", "\n", "- Documentation:\n", "You submit all receipts (flights, taxis, hotel, meals, etc.) and a completed expense report for reimbursement."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import required libraries\n", "\n", "Import the necessary libraries and modules for the notebook."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "from azure.ai.inference import ChatCompletionsClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from semantic_kernel.kernel import Kernel\n", "from semantic_kernel.agents import AgentGroupChat\n", "from openai import AsyncOpenAI\n", "from semantic_kernel.agents import ChatCompletionAgent, AgentGroupChat\n", "\n", "\n", "from semantic_kernel.contents.utils.author_role import AuthorRole\n", "from semantic_kernel.agents.strategies import SequentialSelectionStrategy, DefaultTerminationStrategy\n", "from semantic_kernel.contents.chat_message_content import ChatMessageContent\n", "from semantic_kernel.contents import ImageContent, TextContent\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion, OpenAIChatPromptExecutionSettings\n", "\n", "from semantic_kernel.functions import kernel_function, KernelArguments\n", "from pydantic import BaseModel, Field\n", "from typing import List\n", "from azure.ai.inference.models import SystemMessage, UserMessage, TextContentItem, ImageContentItem, ImageUrl, ImageDetailLevel\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def _create_kernel_with_chat_completion(service_id: str) -> Kernel:\n", "    kernel = Kernel()\n", "   \n", "    client = AsyncOpenAI(\n", "    api_key=os.environ[\"GITHUB_TOKEN\"], base_url=\"https://models.inference.ai.azure.com/\")\n", "    kernel.add_service(\n", "        OpenAIChatCompletion(\n", "            ai_model_id=\"gpt-4o-mini\",\n", "            async_client=client,\n", "            service_id=\"open_ai\"\n", "        )\n", "    )\n", "\n", "    kernel.add_service(\n", "        OpenAIChatCompletion(\n", "            ai_model_id=\"gpt-4o\",\n", "            async_client=client,\n", "            service_id=\"gpt-4o\"\n", "        )\n", "    )\n", "\n", "    return kernel"]}, {"cell_type": "markdown", "metadata": {}, "source": [" ## Define Expense Models\n", "\n", " Create a Pydantic model for individual expenses and an ExpenseFormatter class to convert a user query into structured expense data.\n", "\n", " Each expense will be represented in the format:\n", " `{'date': '07-Mar-2025', 'description': 'flight to destination', 'amount': 675.99, 'category': 'Transportation'}`\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class Expense(BaseModel):\n", "    date: str = Field(..., description=\"Date of expense in dd-MMM-yyyy format\")\n", "    description: str = Field(..., description=\"Expense description\")\n", "    amount: float = Field(..., description=\"Expense amount\")\n", "    category: str = Field(..., description=\"Expense category (e.g., Transportation, Meals, Accommodation, Miscellaneous)\")\n", "\n", "class ExpenseFormatter(BaseModel):\n", "    raw_query: str = Field(..., description=\"Raw query input containing expense details\")\n", "    \n", "    def parse_expenses(self) -> List[Expense]:\n", "        \"\"\"\n", "        Parses the raw query into a list of Expense objects.\n", "        Expected format: \"date|description|amount|category\" separated by semicolons.\n", "        \"\"\"\n", "        expense_list = []\n", "        for expense_str in self.raw_query.split(\";\"):\n", "            if expense_str.strip():\n", "                parts = expense_str.strip().split(\"|\")\n", "                if len(parts) == 4:\n", "                    date, description, amount, category = parts\n", "                    try:\n", "                        expense = Expense(\n", "                            date=date.strip(),\n", "                            description=description.strip(),\n", "                            amount=float(amount.strip()),\n", "                            category=category.strip()\n", "                        )\n", "                        expense_list.append(expense)\n", "                    except ValueError as e:\n", "                        print(f\"[LOG] Parse Error: Invalid data in '{expense_str}': {e}\")\n", "        return expense_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining Agents - Generating the Email\n", "\n", "Create an agent class to generate an email for submitting an expense claim.\n", "- This agent uses the `kernel_function` decorator to define a function that generates an email for submitting an expense claim.\n", "- It calculates the total amount of the expenses and formats the details into an email body."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class ExpenseEmailAgent:\n", "\n", "    @kernel_function(description=\"Generate an email to submit an expense claim to the Finance Team\")\n", "    async def generate_expense_email(expenses):\n", "        total_amount = sum(expense['amount'] for expense in expenses)\n", "        email_body = \"Dear Finance Team,\\n\\n\"\n", "        email_body += \"Please find below the details of my expense claim:\\n\\n\"\n", "        for expense in expenses:\n", "            email_body += f\"- {expense['description']}: ${expense['amount']}\\n\"\n", "        email_body += f\"\\nTotal Amount: ${total_amount}\\n\\n\"\n", "        email_body += \"Receipts for all expenses are attached for your reference.\\n\\n\"\n", "        email_body += \"Thank you,\\n[Your Name]\"\n", "        return email_body"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Agent for Extracting Travel Expenses from Receipt Images\n", "\n", "Create an agent class to extract travel expenses from receipt images.\n", "- This agent uses the `kernel_function` decorator to define a function that extracts travel expenses from receipt images.\n", "- Convert the receipt image to text using OCR (Optical Character Recognition) and extract relevant information such as date, description, amount, and category."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class OCRAgentPlugin:\n", "    def __init__(self):\n", "        self.client = ChatCompletionsClient(\n", "            endpoint=\"https://models.inference.ai.azure.com/\",\n", "            credential=AzureKeyCredential(os.environ.get(\"GITHUB_TOKEN\")),\n", "        )\n", "        self.model_name = \"gpt-4o\"\n", "\n", "    @kernel_function(description=\"Extract structured travel expense data from receipt.jpg using gpt-4o-model\")\n", "    def extract_text(self, image_path: str = \"receipt.jpg\") -> str:\n", "        try:\n", "            image_url_str = str(ImageUrl.load(image_file=image_path, image_format=\"jpg\", detail=ImageDetailLevel.HIGH))\n", "\n", "            prompt = (\n", "                \"You are an expert OCR assistant specialized in extracting structured data from receipt images. \"\n", "                \"Analyze the provided receipt image and extract travel-related expense details in the format: \"\n", "                \"'date|description|amount|category' separated by semicolons. \"\n", "                \"Follow these rules: \"\n", "                \"- Date: Convert dates (e.g., '4/4/22') to 'dd-MMM-yyyy' (e.g., '04-Apr-2022'). \"\n", "                \"- Description: Extract item names (e.g., 'Carlson's Drylawn', 'Peigs transaction Probiotics'). \"\n", "                \"- Amount: Use numeric values (e.g., '4.50' from '$4.50' or '4.50 dollars'). \"\n", "                \"- Category: Infer from context (e.g., 'Meals' for food, 'Transportation' for travel, 'Accommodation' for lodging, 'Miscellaneous' otherwise). \"\n", "                \"Ignore totals, subtotals, or service charges unless they are itemized expenses. \"\n", "                \"If no expenses are found, return 'No expenses detected'. \"\n", "                \"Return only the structured data, no additional text.\"\n", "            )\n", "            response = self.client.complete(\n", "                messages=[\n", "                    SystemMessage(content=prompt),\n", "                    UserMessage(content=[\n", "                        TextContentItem(text=\"Extract travel expenses from this receipt image.\"),\n", "                        ImageContentItem(image_url=ImageUrl(url=image_url_str))\n", "                    ])\n", "                ],\n", "                model=self.model_name,\n", "                temperature=0.1,\n", "                max_tokens=2048\n", "            )\n", "            extracted_text = response.choices[0].message.content\n", "            return extracted_text\n", "        except Exception as e:\n", "            error_msg = f\"[LOG] OCR Plugin: Error processing image: {str(e)}\"\n", "            print(error_msg)\n", "            return error_msg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing Expenses\n", "\n", "Define an asynchronous function to process the expenses by creating and registering the necessary agents and then invoking them.\n", "- This function processes the expenses by loading environment variables, creating the necessary agents, and registering them as plugins.\n", "- It creates a group chat with the two agents and sends a prompt message to generate the email and pie chart based on the expenses data.\n", "- It handles any errors that occur during the chat invocation and ensures proper cleanup of the agents."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def process_expenses():\n", "    load_dotenv()\n", "    settings_slm = OpenAIChatPromptExecutionSettings(service_id=\"gpt-4o\")\n", "    settings_llm = OpenAIChatPromptExecutionSettings(service_id=\"open_ai\")  # Fixed typo in service_id\n", "    \n", "    ocr_agent = ChatCompletionAgent(\n", "        kernel=_create_kernel_with_chat_completion(\"ocrAgent\"),\n", "        name=\"ocr_agent\",\n", "        instructions=\"Extract travel expense data from the receipt image in the prompt using the 'extract_text' function from the 'ocrAgent' plugin. Return the data in the format 'date|description|amount|category' separated by semicolons.\",\n", "        arguments=KernelArguments(settings=settings_slm)\n", "    )\n", "    \n", "       \n", "    email_agent = ChatCompletionAgent(\n", "            kernel=_create_kernel_with_chat_completion(\"expenseEmailAgent\"),\n", "            name=\"email_agent\",\n", "            instructions=\"Take the travel expense data from the previous agent and generate a professional expense claim email using the 'generate_expense_email' function from the 'expenseEmailAgent' plugin, then pass the data forward.\",\n", "            arguments=KernelArguments(\n", "                settings=settings_llm)\n", "        )\n", "\n", "\n", "    kernel = Kernel()\n", "\n", "    # Use fixed path to receipt.jpg in the same folder\n", "    image_path = \"./receipt.jpg\"\n", "    \n", "    # Create a structured message with text and image content for OCR processing\n", "    image_url_str = f\"file://{image_path}\"\n", "    \n", "    # Using the correct format for multi-modal content\n", "    user_message = ChatMessageContent(\n", "        role=AuthorRole.USER,\n", "        items=[\n", "            TextContent(text=\"\"\"\n", "            Please extract the raw text from this receipt image, focusing on travel expenses like dates, descriptions, amounts, and categories (e.g., Transportation, Accommodation, Meals, Miscellaneous).\n", "            Then generate a professional expense claim email.\n", "                        \"\"\"),\n", "            ImageContent.from_image_file(path=image_path)\n", "        ]\n", "    )\n", "\n", "    # Register plugins with the kernel\n", "    kernel.add_plugin(OCRAgentPlugin(), plugin_name=\"ocrAgent\")\n", "    kernel.add_plugin(ExpenseEmailAgent(), plugin_name=\"expenseEmailAgent\")\n", "\n", "    # Create group chat\n", "    chat = AgentGroupChat(\n", "        agents=[ocr_agent, email_agent],\n", "        selection_strategy=SequentialSelectionStrategy(initial_agent=ocr_agent),\n", "        termination_strategy=DefaultTerminationStrategy(maximum_iterations=1)\n", "    )\n", "\n", "    # Add user message with prompt\n", "    await chat.add_chat_message(user_message)\n", "    print(f\"# User message added to chat with receipt image\")\n", "\n", "    async for content in chat.invoke():\n", "        print(f\"# Agent - {content.name or '*'}: '{content.content}'\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main function\n", "\n", "Define the main function to clear the console and run the `process_expenses` function asynchronously."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# User message added to chat with receipt image\n", "# Agent - ocr_agent: 'The receipt primarily seems to capture costs for meals and beverages. Below is the extracted travel expense data:\n", "\n", "**Travel Expense Data:**  \n", "`2 May '22|Meals at restaurant|75.15|Meals`\n", "\n", "---\n", "\n", "**Professional Expense Claim Email Draft:**  \n", "\n", "**Subject:** Expense <PERSON>m for Meals – 2 May 2022  \n", "\n", "Dear [Recipient's Name],  \n", "\n", "I am submitting an expense claim for a meal incurred during a business-related trip. Below are the details:  \n", "\n", "- **Date:** 2 May 2022  \n", "- **Expense Description:** Meals at a restaurant  \n", "- **Amount:** $75.15  \n", "- **Category:** Meals  \n", "\n", "Please find the attached receipt for your reference. Kindly process the reimbursement at your earliest convenience. Let me know if you require additional information.  \n", "\n", "Thank you for your assistance.  \n", "\n", "Best regards,  \n", "[Your Name]  \n", "[Your Contact Information]  \n", "\n", "Let me know if you need further revisions or additional details!'\n"]}], "source": ["async def main():\n", "    # Clear the console\n", "    os.system('cls' if os.name=='nt' else 'clear')\n", "\n", "    # Run the async agent code\n", "    await process_expenses()\n", "\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}