{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from typing import Annotated\n", "\n", "from dotenv import load_dotenv\n", "\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import FunctionCallContent, FunctionResultContent, StreamingTextContent\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.functions import kernel_function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a sample plugin for the sample\n", "class DestinationsPlugin:\n", "    \"\"\"A List of Destinations for vacation.\"\"\"\n", "\n", "    @kernel_function(description=\"Provides a list of vacation destinations.\")\n", "    def get_destinations(self) -> Annotated[str, \"Returns the specials from the menu.\"]:\n", "        return \"\"\"\n", "        Barcelona, Spain\n", "        Paris, France\n", "        Berlin, Germany\n", "        Tokyo, Japan\n", "        New York, USA\n", "        \"\"\"\n", "\n", "    @kernel_function(description=\"Provides available flight times for a destination.\")\n", "    def get_flight_times(\n", "        self, destination: Annotated[str, \"The destination to check flight times for.\"]\n", "    ) -> Annotated[str, \"Returns flight times for the specified destination.\"]:\n", "        # Return HTTP ERROR 404\n", "        return \"HTTP ERROR 404: Flight times service is currently unavailable.\"\n", "\n", "    @kernel_function(description=\"Backup function that provides available flight times for a destination.\")\n", "    def get_flight_times_backup(\n", "        self, destination: Annotated[str, \"The destination to check flight times for.\"]\n", "    ) -> Annotated[str, \"Returns flight times for the specified destination.\"]:\n", "        flight_times = {\n", "            \"Barcelona\": [\"08:30 AM\", \"02:15 PM\", \"10:45 PM\"],\n", "            \"Paris\": [\"06:45 AM\", \"12:30 PM\", \"07:15 PM\"],\n", "            \"Berlin\": [\"07:20 AM\", \"01:45 PM\", \"09:30 PM\"],\n", "            \"Tokyo\": [\"11:00 AM\", \"05:30 PM\", \"11:55 PM\"],\n", "            \"New York\": [\"05:15 AM\", \"03:00 PM\", \"08:45 PM\"]\n", "        }\n", "\n", "        # Extract just the city name from input that might contain country\n", "        city = destination.split(',')[0].strip()\n", "\n", "        if city in flight_times:\n", "            times = \", \".join(flight_times[city])\n", "            return f\"Flight times for {city}: {times}\"\n", "        else:\n", "            return f\"No flight information available for {city}.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "# client = AsyncOpenAI(\n", "#     api_key=os.getenv(\"GITHUB_TOKEN\"), \n", "#     base_url=\"https://models.inference.ai.azure.com/\",\n", "# )\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    # async_client=client,\n", "    api_key=os.getenv(\"OPENAI_API_KEY\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AGENT_NAME = \"TravelAgent\"\n", "AGENT_INSTRUCTIONS = \"\"\" \\\n", "\"You are Flight Booking Agent that provides information about available flights and gives travel activity suggestions when asked.\n", "Travel activity suggestions should be specific to customer, location and amount of time at location.\n", "\n", "You have access to the following tools to help users plan their trips:\n", "1. get_destinations: Returns a list of available vacation destinations that users can choose from.\n", "2. get_flight_times: Provides available flight times for specific destinations.\n", "3. get_flight_times_backup: Backup function that provides available flight times when the primary service is down.\n", "\n", "Your process for assisting users:\n", "- When users inquire about flight booking, book the earliest flight available for the destination they choose using get_flight_times.\n", "- If get_flight_times returns an error message, immediately use get_flight_times_backup with the same destination parameter to retrieve flight information.\n", "- Since you do not have access to a booking system, DO NOT ask to proceed with booking, just assume you have booked the flight.\n", "- Use any past conversation history to understand user preferences and consider them when making suggestions on flights and activities. When making a suggestion, be very clear on why you are making this suggestion if based on a user preference.\n", "\n", "Guidelines:\n", "- Use the exact destination names when using tools (Barcelona, Paris, Berlin, Tokyo, New York)\n", "- <PERSON><PERSON><PERSON> in a helpful and enthusiastic manner about travel possibilities\n", "- Always seek feedback to ensure your suggestions meet the user's expectations\n", "- Acknowledge when a request falls outside your capabilities\n", "- For better formatting, always display flight times in a list format\n", "- When giving any timed suggestions, reflect if the time frames are reasonable. Respond again if not.\n", "- If the flight times service is down, inform the user that you're using backup flight data while maintaining a positive tone.\n", "\n", "Your goal is to help users explore vacation options efficiently and make informed travel decisions by understanding their preferences and providing tailored recommendations.\n", "\"\"\"\n", "# Create the agent\n", "agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    plugins=[DestinationsPlugin()],\n", "    name=AGENT_NAME,\n", "    instructions=AGENT_INSTRUCTIONS,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "\n", "user_inputs = [\n", "    \"Book me a flight to Barcelona\",\n", "]\n", "\n", "# Create a thread to hold the conversation\n", "# If no thread is provided, a new thread will be\n", "# created and returned with the initial response\n", "thread: ChatHistoryAgentThread | None = None\n", "\n", "async def main():\n", "    global thread\n", "    \n", "    for user_input in user_inputs:\n", "        html_output = (\n", "            f\"<div style='margin-bottom:10px'>\"\n", "            f\"<div style='font-weight:bold'>User:</div>\"\n", "            f\"<div style='margin-left:20px'>{user_input}</div></div>\"\n", "        )\n", "\n", "        agent_name = None\n", "        full_response: list[str] = []\n", "        function_calls: list[str] = []\n", "\n", "        # Buffer to reconstruct streaming function call\n", "        current_function_name = None\n", "        argument_buffer = \"\"\n", "\n", "        async for response in agent.invoke_stream(\n", "            messages=user_input,\n", "            thread=thread,\n", "        ):\n", "            thread = response.thread\n", "            agent_name = response.name\n", "            content_items = list(response.items)\n", "\n", "            for item in content_items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    if item.function_name:\n", "                        current_function_name = item.function_name\n", "\n", "                    # Accumulate arguments (streamed in chunks)\n", "                    if isinstance(item.arguments, str):\n", "                        argument_buffer += item.arguments\n", "                elif isinstance(item, FunctionResultContent):\n", "                    # Finalize any pending function call before showing result\n", "                    if current_function_name:\n", "                        formatted_args = argument_buffer.strip()\n", "                        try:\n", "                            parsed_args = json.loads(formatted_args)\n", "                            formatted_args = json.dumps(parsed_args)\n", "                        except Exception:\n", "                            pass  # leave as raw string\n", "\n", "                        function_calls.append(f\"Calling function: {current_function_name}({formatted_args})\")\n", "                        current_function_name = None\n", "                        argument_buffer = \"\"\n", "\n", "                    function_calls.append(f\"\\nFunction Result:\\n\\n{item.result}\")\n", "                elif isinstance(item, StreamingTextContent) and item.text:\n", "                    full_response.append(item.text)\n", "\n", "        if function_calls:\n", "            html_output += (\n", "                \"<div style='margin-bottom:10px'>\"\n", "                \"<details>\"\n", "                \"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"\n", "                \"<div style='margin:10px; padding:10px; background-color:#f8f8f8; \"\n", "                \"border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"\n", "                f\"{chr(10).join(function_calls)}\"\n", "                \"</div></details></div>\"\n", "            )\n", "\n", "        html_output += (\n", "            \"<div style='margin-bottom:20px'>\"\n", "            f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"\n", "            f\"<div style='margin-left:20px; white-space:pre-wrap'>{''.join(full_response)}</div></div><hr>\"\n", "        )\n", "\n", "        display(HTML(html_output))\n", "\n", "await main()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}