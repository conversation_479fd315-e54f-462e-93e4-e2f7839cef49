{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AutoGen Basic Sample \n", "\n", "In this code sample, you will use the [AutoGen](https://aka.ms/ai-agents/autogen) AI Framework to create a basic agent. \n", "\n", "The goal of this sample is to show you the steps that we will later use in the additional code samples when implementing the different agentic patterns. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import the Needed Python Packages "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "from autogen_agentchat.agents import AssistantAgent\n", "from autogen_core.models import UserMessage\n", "from autogen_ext.models.azure import AzureAIChatCompletionClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from autogen_core import CancellationToken\n", "\n", "from autogen_agentchat.messages import TextMessage\n", "from autogen_agentchat.ui import Console\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the Client \n", "\n", "In this sample, we will use [GitHub Models](https://aka.ms/ai-agents-beginners/github-models) for access to the LLM. \n", "\n", "The `model` is defined as `gpt-4o-mini`. Try changing the model to another model available on the GitHub Models marketplace to see the different results. \n", "\n", "As a quick test, we will just run a simple prompt - `What is the capital of France`. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "client = AzureAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    endpoint=\"https://models.inference.ai.azure.com\",\n", "    # To authenticate with the model you will need to generate a personal access token (PAT) in your GitHub settings.\n", "    # Create your PAT token by following instructions here: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens\n", "    credential=AzureKeyCredential(os.getenv(\"GITHUB_TOKEN\")),\n", "    model_info={\n", "        \"json_output\": True,\n", "        \"function_calling\": True,\n", "        \"vision\": True,\n", "        \"family\": \"unknown\",\n", "    },\n", ")\n", "\n", "result = await client.create([UserMessage(content=\"What is the capital of France?\", source=\"user\")])\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining the Agent \n", "\n", "Now that we have set up the `client` and confirmed that it is working, let us create an `AssistantAgent`. Each agent can be assigned a: \n", "**name** - A short hand name that will be useful in referencing it in multi-agent flows. \n", "**model_client** - The client that you created in the earlier step. \n", "**tools** - Available tools that the Agent can use to complete a task.\n", "**system_message** - The metaprompt that defines the task, behavior and tone of the LLM. \n", "\n", "You can change the system message to see how the LLM responds. We will cover `tools` in Lesson #4. \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = AssistantAgent(\n", "    name=\"assistant\",\n", "    model_client=client,\n", "    tools=[],\n", "    system_message=\"You are a travel agent that plans great vacations\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Agent \n", "\n", "The below function will run the agent. We use the the `on_message` method to update the Agent's state with the new message. \n", "\n", "In this case, we update the state with a new message from the user which is `\"Plan me a great sunny vacation\"`.\n", "\n", "You can change the message content to see how the LLM responds differently. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "\n", "\n", "async def assistant_run():\n", "    # Define the query\n", "    user_query = \"Plan me a great sunny vacation\"\n", "\n", "    # Start building HTML output\n", "    html_output = \"<div style='margin-bottom:10px'>\"\n", "    html_output += \"<div style='font-weight:bold'>User:</div>\"\n", "    html_output += f\"<div style='margin-left:20px'>{user_query}</div>\"\n", "    html_output += \"</div>\"\n", "\n", "    # Execute the agent response\n", "    response = await agent.on_messages(\n", "        [TextMessage(content=user_query, source=\"user\")],\n", "        cancellation_token=CancellationToken(),\n", "    )\n", "\n", "    # Add agent response to HTML\n", "    html_output += \"<div style='margin-bottom:20px'>\"\n", "    html_output += \"<div style='font-weight:bold'>Assistant:</div>\"\n", "    html_output += f\"<div style='margin-left:20px; white-space:pre-wrap'>{response.chat_message.content}</div>\"\n", "    html_output += \"</div>\"\n", "\n", "    # Display formatted HTML\n", "    display(HTML(html_output))\n", "\n", "# Run the function\n", "await assistant_run()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}