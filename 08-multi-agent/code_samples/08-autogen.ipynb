{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from autogen_agentchat.agents import AssistantAgent\n", "from autogen_core.models import UserMessage\n", "from autogen_ext.models.azure import AzureAIChatCompletionClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from autogen_core import CancellationToken\n", "from autogen_agentchat.base import TaskResult\n", "\n", "from autogen_agentchat.messages import TextMessage\n", "from autogen_agentchat.ui import Console\n", "\n", "\n", "from autogen_agentchat.conditions import TextMentionTermination\n", "from autogen_agentchat.teams import RoundRobinGroupChat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = AzureAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    endpoint=\"https://models.inference.ai.azure.com\",\n", "    # To authenticate with the model you will need to generate a personal access token (PAT) in your GitHub settings.\n", "    # Create your PAT token by following instructions here: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens\n", "    credential=AzureKeyCredential(os.environ[\"GITHUB_TOKEN\"]),\n", "    model_info={\n", "        \"json_output\": True,\n", "        \"function_calling\": True,\n", "        \"vision\": True,\n", "        \"family\": \"unknown\",\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontdesk_agent = AssistantAgent(\n", "    \"planner_agent\",\n", "    model_client=client,\n", "    description=\"A helpful assistant that can plan trips.\",\n", "    system_message=\"\"\"\n", "    You are a Front Desk Travel Agent with ten years of experience and are known for brevity as you deal with many customers.\n", "    The goal is to provide the best activities and locations for a traveler to visit.\n", "    Only provide a single recommendation per response.\n", "    You're laser focused on the goal at hand.\n", "    Don't waste time with chit chat.\n", "    Consider suggestions when refining an idea.\"\"\",\n", ")\n", "\n", "concierge_agent = AssistantAgent(\n", "    \"concierge_agent\",\n", "    model_client=client,\n", "    description=\"A local assistant that can suggest local activities or places to visit.\",\n", "    system_message=\"\"\"\n", "    You are an are hotel concierge who has opinions about providing the most local and authentic experiences for travelers.\n", "    The goal is to determine if the front desk travel agent has recommended the best non-touristy experience for a traveler.\n", "    If so, respond with 'APPROVE'\n", "    If not, provide insight on how to refine the recommendation without using a specific example. \n", "    \"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["termination = TextMentionTermination(\"APPROVE\")\n", "team = RoundRobinGroupChat(\n", "    [frontdesk_agent, concierge_agent], termination_condition=termination\n", ")\n", "\n", "async for message in team.run_stream(task=\"I would like to plan a trip to Paris.\"):\n", "    if isinstance(message, TaskResult):\n", "        print(\"Stop Reason:\", message.stop_reason)\n", "    else:\n", "        print(message)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}