{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## **Samples: Multi-AI Agents for Booking hotel**\n", "\n", "In today's fast-paced world, planning a business trip involves more than just booking a flight and a hotel room. It requires a level of coordination and efficiency that can be challenging to achieve. This is where Multi-AI Agents come into play, revolutionizing the way we manage our travel needs.\n", "\n", "Imagine having a team of intelligent agents at your disposal, working together to handle every aspect of your trip with precision and ease. With our advanced AI technology, we have created specialized agents for booking services and itinerary arrangement, ensuring a seamless and stress-free travel experience. \n", "\n", "This is a basic scenario. When planning a business trip, we need to consult with a business travel agent to obtain air ticket information, hotel information, etc. Through AI Agents, we can build agents for booking services and agents for itinerary arrangement to collaborate and improve the level of intelligence. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Initialize the Azure AI Agent Service and get configuration information from **.env**\n", "\n", "### **.env** \n", "\n", "Create a .env file \n", "\n", "**.env** contains the connection string of Azure AI Agent Service, the model used by AOAI, and the corresponding Google API Search service API, ENDPOINT, etc.\n", "\n", "- **AZURE_AI_AGENT_MODEL_DEPLOYMENT_NAME** = \"Your Azure AI Agent Service Model Deployment Name\"\n", "\n", "[**NOTE**] You will need a model with 100,000 Rate Limit (Tokens per minute)  Rate Limit of 600 (Request per minute)\n", "\n", "  You can get model in Azure AI Foundry - Model and Endpoint. \n", "\n", "\n", "- **AZURE_AI_AGENT_PROJECT_CONNECTION_STRING** = \"Your Azure AI Agent Service Project Connection String\"\n", "\n", "  You can get the project connection string in your project overview in  AI ​​Foundry Portal Screen.\n", "\n", "- **SERPAPI_SEARCH_API_KEY** = \"Your SERPAPI Search API KEY\"\n", "- **SERPAPI_SEARCH_ENDPOINT** = \"Your SERPAPI Search Endpoint\"\n", "\n", "To get the Model Deployment Name and Project Connection String of Azure AI Agent Service, you need to create Azure AI Agent Service. It is recommended to use [this template](https://portal.azure.com/#create/Microsoft.Template/uri/https%3A%2F%2Fraw.githubusercontent.com%2Ffosteramanda%2Fazure-agent-quickstart-templates%2Frefs%2Fheads%2Fmaster%2Fquickstarts%2Fmicrosoft.azure-ai-agent-service%2Fstandard-agent%2Fazuredeploy.json) to create it directly （***Note:*** Azure AI Agent Service is currently set in a limited region. It is recommended that you refer to [this link](https://learn.microsoft.com/en-us/azure/ai-services/agents/concepts/model-region-support) to set the region)\n", "\n", "Agent needs to access SERPAPI. It is recommended to register using [this link](https://serpapi.com/searches). After registration, you can obtain a unique API KEY and ENDPOINT"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Login to Azure \n", "\n", "You Now need to login into Azure Open a terminal in VScode and run the `az login` command"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup \n", "\n", "To run this notebook, you will need to install the following libraries. Here is a list of the required libraries and the corresponding pip install commands:\n", "\n", "azure-identity: For Azure authentication.\n", "requests: For making HTTP requests.\n", "semantic-kernel: For the semantic kernel framework (assuming this is a custom or specific library, you might need to install it from a specific source or repository)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install azure-identity\n", "!pip install requests\n", "!pip install semantic-kernel\n", "!pip install --upgrade semantic_kernel\n", "!pip install azure-cli"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation: \n", "import asyncio: This imports the asyncio module, which provides support for asynchronous programming in Python. It allows you to write concurrent code using the async and await syntax.\n", "from typing import Annotated: This imports the Annotated type from the typing module. Annotated is used to add metadata to type hints, which can be useful for various purposes such as validation, documentation, or tooling"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["import asyncio,os\n", "from typing import Annotated"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "By using from dotenv import load_dotenv and load_dotenv(), you can easily manage configuration settings and sensitive information (like API keys and database URLs) in a .env file, keeping them separate from your source code and making your application more secure and easier to configure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "\n", "Import Statement: from azure.identity.aio import DefaultAzureCredential: This imports the DefaultAzureCredential class from the azure.identity.aio module. The aio part of the module name indicates that it is designed for asynchronous operations.\n", "\n", "Purpose of DefaultAzureCredential: The DefaultAzureCredential class is part of the Azure SDK for Python. It provides a default way to authenticate with Azure services. It attempts to authenticate using multiple methods in a specific order, such as environment variables, managed identity, and Azure CLI credentials.\n", "\n", "Asynchronous Operations:The aio module indicates that the DefaultAzureCredential class supports asynchronous operations. This means you can use it with asyncio to perform non-blocking authentication requests."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["from azure.identity.aio import DefaultAzureCredential"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "Imports various modules and classes from the semantic_kernel package. Here's a breakdown of each import:\n", "\n", "AgentGroupChat from semantic_kernel.agents: This class handles functionalities related to group chat for AI agents. AzureAIAgent and AzureAIAgentSettings from semantic_kernel.agents.azure_ai\n", "\n", "AzureAIAgent: This class is used to create and manage AI agents that utilize Azure AI services.\n", "\n", "AzureAIAgentSettings: This class is used to configure settings for the AzureAIAgent. TerminationStrategy from semantic_kernel.agents.strategies.termination.termination_strategy:\n", "\n", "This class defines strategies for terminating the execution of AI agents under certain conditions. ChatMessageContent from semantic_kernel.contents.chat_message_content:\n", "\n", "This class is used to handle the content of chat messages.\n", "AuthorRole from semantic_kernel.contents.utils.author_role:\n", "\n", "This class defines different roles for authors in the context of chat messages. \n", "\n", "kernel_function from semantic_kernel.functions.kernel_function_decorator: This decorator is used to define kernel functions, which are functions that can be executed within the semantic kernel framework.\n", "These imports set up the necessary components for creating and managing AI agents that can interact in a group chat environment, possibly for tasks such as booking hotels or similar activities."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["from semantic_kernel.agents import AgentGroupChat\n", "from semantic_kernel.agents import AzureAIAgent, AzureAIAgentSettings\n", "from semantic_kernel.agents.strategies.termination.termination_strategy import TerminationStrategy\n", "from semantic_kernel.contents import ChatMessageContent\n", "from semantic_kernel.contents import AuthorRole\n", "from semantic_kernel.functions.kernel_function_decorator import kernel_function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "Next we import the CodeInterpreterTool class from the azure.ai.projects.models module. \n", "\n", "CodeInterpreterTool: This class is part of the Azure AI SDK and is used to interpret and execute code within the context of AI projects. It provides functionalities for running code snippets, analyzing code, or integrating code execution within AI workflows.\n", "This import sets up the necessary component for utilizing the CodeInterpreterTool in your project, which could be useful for tasks that involve interpreting and executing code dynamically."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["from azure.ai.projects.models import CodeInterpreterTool"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation: \n", "The ApprovalTerminationStrategy class provides a specific strategy for terminating an AI agent's operation. The agent will terminate if the last message in its interaction history contains the word \"saved\". This could be useful in scenarios where the agent's task is considered complete once it receives confirmation that something has been \"saved\".Define the interaction method. After the reservation plan is saved, it can be stopped when receiving the saved signal"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["class ApprovalTerminationStrategy(TerminationStrategy):\n", "    \"\"\"A strategy for determining when an agent should terminate.\"\"\"\n", "\n", "    async def should_agent_terminate(self, agent, history):\n", "        \"\"\"Check if the agent should terminate.\"\"\"\n", "        return \"saved\" in history[-1].content.lower()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "\n", "The line of code initializes an AzureAIAgentSettings object with default or predefined settings by calling the create() method. This settings object (ai_agent_settings) can then be used to configure and manage an AzureAIAgent instance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["ai_agent_settings = AzureAIAgentSettings.create()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "By importing the requests library, you can easily make HTTP requests and interact with web services in your Python code."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["import requests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "This is a variable that stores the API key for accessing a SERP (Search Engine Results Page) API service. An API key is a unique identifier used to authenticate requests associated with your account.\n", "\n", "'GOOGLE_SEARCH_API_KEY': This is a placeholder string. You need to replace ''GOOGLE_SEARCH_API_KEY' with your actual SERP API key.\n", "\n", "Purpose: The purpose of this line is to store the API key in a variable so that it can be used to authenticate requests to the SERP API service. The API key is required to access the service and perform searches.\n", "\n", "How to Get a SERP API Key: To get a SERP API key, follow these general steps at https://serpapi.com (the exact steps may vary depending on the specific SERP API service you are using):\n", "\n", "Choose a SERP API Service: There are several SERP API services available, such as SerpAPI, Google Custom Search JSON API, and others. Choose the one that best fits your needs.\n", "\n", "Sign Up for an Account:\n", "\n", "Go to the website of the chosen SERP API service https://www.serpapi.com and sign up for an account. You may need to provide some basic information and verify your email address.\n", "\n", "Create an API Key:\n", "\n", "After signing up, log in to your account and navigate to the API section or dashboard. Look for an option to create or generate a new API key.\n", "Copy the API Key:\n", "\n", "Once the API key is generated, copy it. This key will be used to authenticate your requests to the SERP API service.\n", "Replace the Placeholder:\n", "\n", "Replace the placeholder in your .env file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["SERPAPI_SEARCH_API_KEY=os.getenv('SERPAPI_SEARCH_API_KEY')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["SERPAPI_SEARCH_ENDPOINT = os.getenv('SERPAPI_SEARCH_ENDPOINT')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "The BookingPlugin class provides methods for booking hotels and flights using the Serpapi.com Google Search API. It constructs the necessary parameters, sends API requests, and processes the responses to return relevant booking information. The API key (SERPAPI_SEARCH_API_KEY) and endpoint (SERPAPI_SEARCH_ENDPOINT) are used to authenticate and send requests to the Google Search API."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["# Define Booking Plugin\n", "class BookingPlugin:\n", "    \"\"\"Booking Plugin for customers\"\"\"\n", "    @kernel_function(description=\"booking hotel\")\n", "    def booking_hotel(self,query: Annotated[str, \"The name of the city\"], check_in_date: Annotated[str, \"Hotel Check-in Time\"], check_out_date: Annotated[str, \"Hotel Check-in Time\"])-> Annotated[str, \"Return the result of booking hotel infomation\"]:\n", "\n", "        params = {\n", "            \"engine\": \"google_hotels\",\n", "            \"q\": query,\n", "            \"check_in_date\": check_in_date,\n", "            \"check_out_date\": check_out_date,\n", "            \"adults\": \"2\",\n", "            \"currency\": \"USD\",\n", "            \"gl\": \"us\",\n", "            \"hl\": \"en\",\n", "            \"api_key\": SERPAPI_SEARCH_API_KEY\n", "        }\n", "\n", "        response = requests.get(SERPAPI_SEARCH_ENDPOINT, params=params)\n", "        if response.status_code == 200:\n", "            response = response.json()\n", "            return response[\"properties\"]\n", "        else:\n", "            return None\n", "\n", "    \n", "    @kernel_function(description=\"booking fight\")\n", "    def  booking_fight(self,origin: Annotated[str, \"The name of Departure\"], destination: Annotated[str, \"The name of Destination\"], outbound_date: Annotated[str, \"The date of outbound\"], return_date: Annotated[str, \"The date of Return_date\"])-> Annotated[str, \"Return the result of booking fight infomation\"]:\n", "        \n", "        go_params = {\n", "            \"engine\": \"google_flights\",   \n", "            \"departure_id\": origin,\n", "            \"arrival_id\": destination,\n", "            \"outbound_date\": outbound_date,\n", "            \"return_date\": return_date,  \n", "            \"currency\": \"USD\",\n", "            \"hl\": \"en\",\n", "            \"api_key\": SERPAPI_SEARCH_API_KEY  \n", "        }\n", "\n", "        print(go_params)\n", "\n", "        go_response = requests.get(SERPAPI_SEARCH_ENDPOINT, params=go_params)\n", "\n", "\n", "        result = ''\n", "\n", "        if go_response.status_code == 200:\n", "            response = go_response.json()\n", "\n", "            result += \"# outbound \\n \" + str(response)\n", "        else:\n", "            print('error!!!')\n", "            # return None\n", "\n", "        \n", "        back_params = {\n", "            \"engine\": \"google_flights\",   \n", "            \"departure_id\": destination,\n", "            \"arrival_id\": origin,\n", "            \"outbound_date\": return_date,\n", "            \"return_date\": return_date,  \n", "            \"currency\": \"USD\",\n", "            \"hl\": \"en\",\n", "            \"api_key\": SERPAPI_SEARCH_API_KEY  \n", "        }\n", "\n", "\n", "        print(back_params)\n", "\n", "\n", "        back_response = requests.get(SERPAPI_SEARCH_ENDPOINT, params=back_params)\n", "\n", "\n", "\n", "        if back_response.status_code == 200:\n", "            response = back_response.json()\n", "\n", "            result += \"\\n # return \\n\"  + str(response)\n", "\n", "        else:\n", "            print('error!!!')\n", "            # return None\n", "        \n", "        print(result)\n", "\n", "        return result\n", "\n", "        \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "The SavePlugin class provides a method saving_plan to save trip plans using Azure AI services. It sets up Azure credentials, creates an AI agent, processes user inputs to generate and save the trip plan content, and handles file saving and cleanup operations. The method returns \"Saved\" upon successful completion."]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["class SavePlugin:\n", "    \"\"\"Save Plugin for customers\"\"\"\n", "    @kernel_function(description=\"saving plan\")\n", "    async def saving_plan(self,tripplan: Annotated[str, \"The content of trip plan\"])-> Annotated[str, \"Return status of save content\"]:\n", "\n", "        async with (\n", "            DefaultAzureCredential() as creds,\n", "            AzureAIAgent.create_client(\n", "                credential=creds,\n", "                conn_str=ai_agent_settings.project_connection_string.get_secret_value(),\n", "            ) as client,\n", "        ):\n", "\n", "            code_interpreter = CodeInterpreterTool()\n", "            \n", "            agent_definition = await client.agents.create_agent(\n", "                model=ai_agent_settings.model_deployment_name,\n", "                tools=code_interpreter.definitions,\n", "                tool_resources=code_interpreter.resources,\n", "            )\n", "\n", "\n", "            agent = AzureAIAgent(\n", "                client=client,\n", "                definition=agent_definition,\n", "            )\n", "\n", "            thread = await client.agents.create_thread()\n", "\n", "\n", "            user_inputs = [\n", "                \"\"\"\n", "            \n", "                        You are my Python programming assistant. Generate code,save \"\"\"+ tripplan +\n", "                        \n", "                    \"\"\"    \n", "                        and execute it according to the following requirements\n", "\n", "                        1. Save blog content to trip-{YYMMDDHHMMSS}.md\n", "\n", "                        2. give me the download this file link\n", "                    \"\"\"\n", "            ]\n", "\n", "\n", "\n", "            try:\n", "                for user_input in user_inputs:\n", "                    # Add the user input as a chat message\n", "                    await agent.add_chat_message(\n", "                        thread_id=thread.id, message=ChatMessageContent(role=AuthorRole.USER, content=user_input)\n", "                    )\n", "                    print(f\"# User: '{user_input}'\")\n", "                    # Invoke the agent for the specified thread\n", "                    async for content in agent.invoke(thread_id=thread.id):\n", "                        if content.role != AuthorRole.TOOL:\n", "                            print(f\"# Agent: {content.content}\")\n", "\n", "                    \n", "                    messages = await client.agents.list_messages(thread_id=thread.id)\n", "\n", "                    # OpenAIPageableListOfThreadMessage\n", "                    # OpenAIPageableListOfThreadMessage\n", "\n", "\n", "                    for file_path_annotation in messages.file_path_annotations:\n", "\n", "                            file_name = os.path.basename(file_path_annotation.text)\n", "\n", "                            await client.agents.save_file(file_id=file_path_annotation.file_path.file_id, file_name=file_name,target_dir=\"./trip\")\n", "\n", "                    \n", "            finally:\n", "                await client.agents.delete_thread(thread.id)\n", "                await client.agents.delete_agent(agent.id)\n", "\n", "\n", "        return \"Saved\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explanation:\n", "This code sets up Azure AI agents to handle booking flights and hotels, and saving trip plans based on user inputs. It uses Azure credentials to create and configure the agents, processes user inputs through a group chat, and ensures proper cleanup after the tasks are completed. The agents use specific plugins (BookingPlugin and SavePlugin) to perform their respective tasks.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}}, "outputs": [], "source": ["async with (\n", "    DefaultAzureCredential() as creds,\n", "    AzureAIAgent.create_client(\n", "        credential=creds,\n", "        conn_str=ai_agent_settings.project_connection_string.get_secret_value(),\n", "    ) as client,\n", "):\n", "    BOOKING_AGENT_NAME = \"BookingAgent\"\n", "    BOOKING_AGENT_INSTRUCTIONS = \"\"\"\n", "    You are a booking agent. Help me book flights or hotels.\n", "\n", "    Thought: Please understand the user's intention and confirm whether to use the reservation system to complete the task.\n", "\n", "    Actions:\n", "    - For flight bookings, convert the departure and destination names into airport codes.\n", "    - Use the appropriate API for hotel or flight bookings. Verify that all necessary parameters are available. If any parameters are missing, ask the user to provide them. If all parameters are complete, call the corresponding function.\n", "    - If the task is not related to hotel or flight booking, respond with the final answer only.\n", "    - Output the results using a markdown table:\n", "      - For flight bookings, output separate outbound and return contents in the order of:\n", "        Departure Airport | Airline | Flight Number | Departure Time | Arrival Airport | Arrival Time | Duration | Airplane | Travel Class | Price (USD) | Legroom | Extensions | Carbon Emissions (kg).\n", "      - For hotel bookings, output in the order of:\n", "        Property Name | Property Description | Check-in Time | Check-out Time | Prices | Nearby Places | Hotel Class | GPS Coordinates.\n", "    \"\"\"\n", "\n", "    SAVE_AGENT_NAME = \"SaveAgent\"\n", "    SAVE_AGENT_INSTRUCTIONS = \"\"\"\n", "    You are a save tool agent. Help me to save the trip plan.\n", "    \"\"\"\n", "\n", "    # Create agent definition\n", "    booking_agent_definition = await client.agents.create_agent(\n", "        model=ai_agent_settings.model_deployment_name,\n", "        name=BOOKING_AGENT_NAME,\n", "        instructions=BOOKING_AGENT_INSTRUCTIONS,\n", "    )\n", "\n", "    # Create the AzureAI Agent\n", "    booking_agent = AzureAIAgent(\n", "        client=client,\n", "        definition=booking_agent_definition,\n", "        # Optionally configure polling options\n", "        # polling_options=RunPollingOptions(run_polling_interval=timedelta(seconds=1)),\n", "    )\n", "\n", "    # Add the sample plugin to the kernel\n", "    booking_agent.kernel.add_plugin(BookingPlugin(), plugin_name=\"booking\")\n", "\n", "    # Create agent definition\n", "    save_agent_definition = await client.agents.create_agent(\n", "        model=ai_agent_settings.model_deployment_name,\n", "        name=SAVE_AGENT_NAME,\n", "        instructions=SAVE_AGENT_INSTRUCTIONS\n", "    )\n", "\n", "    # Create the AzureAI Agent\n", "    save_agent = AzureAIAgent(\n", "        client=client,\n", "        definition=save_agent_definition,\n", "    )\n", "\n", "    save_agent.kernel.add_plugin(SavePlugin(), plugin_name=\"saving\")\n", "\n", "    user_inputs = [\n", "        \"I have a business trip from London to New York in Feb 20 2025 to Feb 27 2025 ,help me to book a hotel and fight tickets and save it\"\n", "    ]\n", "\n", "    chat = AgentGroupChat(\n", "        agents=[booking_agent, save_agent],\n", "        termination_strategy=ApprovalTerminationStrategy(agents=[save_agent], maximum_iterations=10),\n", "    )\n", "\n", "    try:\n", "        for user_input in user_inputs:\n", "            # Add the user input as a chat message\n", "            await chat.add_chat_message(\n", "                ChatMessageContent(role=AuthorRole.USER, content=user_input)\n", "            )\n", "            print(f\"# User: '{user_input}'\")\n", "\n", "            async for content in chat.invoke():\n", "                print(f\"# {content.role} - {content.name or '*'}: '{content.content}'\")\n", "\n", "            print(f\"# IS COMPLETE: {chat.is_complete}\")\n", "\n", "            print(\"*\" * 60)\n", "            print(\"Chat History (In Descending Order):\\n\")\n", "            async for message in chat.get_chat_messages(agent=save_agent):\n", "                print(f\"# {message.role} - {message.name or '*'}: '{message.content}'\")\n", "    finally:\n", "        await chat.reset()\n", "        await client.agents.delete_agent(save_agent.id)\n", "        await client.agents.delete_agent(booking_agent.id)\n"]}], "metadata": {"kernelspec": {"display_name": "3.12.1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}, "polyglot_notebook": {"kernelInfo": {"defaultKernelName": "csharp", "items": [{"aliases": [], "name": "csharp"}]}}}, "nbformat": 4, "nbformat_minor": 2}