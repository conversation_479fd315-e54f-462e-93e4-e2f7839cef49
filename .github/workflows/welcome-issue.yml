name:  Welcome to the AI Agents for Beginners Course 
on:
  # Trigger the workflow on new issue
  issues:
    types: [opened]
permissions:
  contents: read
  issues: write
jobs:
  asses-issue:
    runs-on: ubuntu-latest
    steps:
      - name: Add Label and thanks comment to Issue
        uses: actions/github-script@v6
        with:
          script: |
            const issueAuthor = context.payload.sender.login
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['needs-review']
            })
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `👋 Thanks for contributing @${ issueAuthor }! We will review the issue and get back to you soon.`
            })
      - name: Auto-assign issue
        uses: pozil/auto-assign-issue@v1
        with:
          repo-token:  ${{ secrets.GITHUB_TOKEN }}
          assignees: koreyspace