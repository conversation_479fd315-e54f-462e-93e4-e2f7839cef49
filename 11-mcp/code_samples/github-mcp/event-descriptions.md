## Event Name: Build your code-first app with Azure AI Agent Service (EMEA/US offering)

## Description

The Azure AI Agent Service is a seamless blend of service and SDK that simplifies the development of robust AI-driven solutions. In this session, you'll learn how to build your own code-first AI agent with Azure that can answer questions, perform data analysis, and integrate external data sources. You'll also explore more complex architectures, including multiple agents working together.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25325/>

---

## Event Name: Transforming Business Processes with Multi-Agent AI using Semantic Kernel

## Description

Discover the power of multi-agent AI systems through live demonstrations and hands-on learning with patterns including group-chat, reflection, selector, and swarm. Harness the Semantic Kernel Process Framework to automate and scale critical business processes, from customer support to project management using Python

## URL
<https://developer.microsoft.com/en-us/reactor/events/25313/>

---

## Event Name: Building Agentic Applications with AutoGen v0.4

## Description

Getting started to build agents and multi-agent teams using AutoGen v0.4. We will cover an overview of the new AutoGen v0.4 architecture and walk you through how to build a multi-agent team with a web-based user interface.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25327/>

---

## Event Name: Prototyping AI Agents with GitHub Models

## Description

Thanks to GitHub Models, all you need to build your first AI Agent is a GitHub account! GitHub Models includes powerful models like OpenAI gpt-4o, DeepSeek-R1, Llama-3.1, and many more, ready to try out in the playground or in your code.
In this session, we'll demonstrate how to connect to GitHub Models from Python, and then build agents using popular Python packages like PydanticAI, AutoGen, and Semantic Kernel.
You can follow along live in GitHub Codespaces, or try the examples yourself anytime after the session.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25481/>

---

## Event Name: Building agents with an army of models from the Azure AI model catalog

## Description

The Azure AI model catalog offers a big variety of models, with different skills and capabilities. While using an off the shelf model to get you started, as developers use more sophisticated workflows, they can leverage specialized models to make the job in their framework of choice. In this presentation we go over the model catalog offering, and how you can build agents that sit of top of an army of models - while not costing you a fortune.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25328/>

---

## Event Name: Multi-Agent API with LangGraph and Azure Cosmos DB

## Description

The rise of multi-agent AI applications is transforming how we build intelligent systems - but how do you architect them for real-world scalability and performance? In this session, we’ll take a deep dive into a production-grade multi-agent application built with LangGraph for agent orchestration, FastAPI for an API layer, and Azure Cosmos DB as the backbone for state management, vector storage, and transactional data.

Through a detailed code walkthrough, you’ll see how to design and implement an agent-driven workflow that seamlessly integrates retrieval-augmented generation (RAG), memory persistence, and dynamic state transitions. We’ll cover:

Agent collaboration with LangGraph for structured reasoning
Real-time chat history storage using Azure Cosmos DB - the same database that powers the chat history in ChatGPT, the fastest-growing AI agent application in history
Vector search for knowledge retrieval with Cosmos DB's native embeddings support
FastAPI’s async capabilities to keep interactions responsive and scalable
By the end of this session, you’ll have a clear blueprint for building and deploying your own scalable, cloud-native multi-agent applications that harness the power of modern AI and cloud infrastructure. Whether you're an AI engineer, cloud architect, or Python developer, this talk will equip you with practical insights and battle-tested patterns to build the next generation of AI-powered applications

## URL
<https://developer.microsoft.com/en-us/reactor/events/25314/>

---

## Event Name: Your First AI Agent in JS with Azure AI Agent Service

## Description

Learn how to build your first AI agent using the JavaScript SDK for Azure AI Agent Service, a fully managed platform that makes development easy. You’ll see how to set it up, connect tools like Azure AI Search, and deploy a simple question-answering agent. With a live demo, you’ll discover how automatic tool calling and managed state simplify the process. Perfect for beginners, this session gives you practical steps and tips to start your AI agent journey with confidence.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25381/>

---

## Event Name: Prompting is the New Scripting: Meet GenAIScript

## Description

jQuery once made web development easier by abstracting away complexities, allowing developers to focus on building rather than battling browser quirks. Today, AI development faces a similar challenge. New patterns emerge constantly and keeping up can be overwhelming, especially as AI tools—especially agentic ones— become more powerful and complex. What if you could leverage cutting-edge AI capabilities to automate tasks using simple, familiar JavaScript abstractions? Enter GenAIScript—a way to integrate AI into your workflow effortlessly, treating prompts like reusable code snippets. In this talk, we’ll explore how GenAIScript makes AI automation agents feel as intuitive as writing JavaScript, helping you streamline repetitive work without the need for deep AI expertise.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25441/>

---

## Event Name: Knowledge-augmented agents with LlamaIndex.TS

## Description

LlamaIndex is known for making it easy to build Retrieval-Augmented Generation (RAG), but our frameworks also make it easy to build agents and multi-agent systems! In this session we'll introduce Workflows, our basic building block for building agentic systems, and build an agent that uses RAG and other tools.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25440/>

---

## Event Name: AI Agents for Java using Azure AI Foundry and GitHub Copilot

## Description

In this session we’ll show you how to embed advanced AI Agent capabilities into your Java applications using Azure AI Foundry, including setting project goals and experimenting with models and securely deploying production-ready solutions at scale. Along the way, you’ll learn how GitHub Copilot (in IntelliJ, VS Code, and Eclipse) can streamline coding and prompt creation, while best practices in model selection, fine-tuning, and agentic workflows ensure responsible and efficient development. Whether you’re new to AI Agents or looking for advanced agent-building techniques, this session will equip you to deliver next-level experiences with the tooling you already know.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25336/>

---

## Event Name: Building Java AI Agents using LangChain4j and Dynamic Sessions

## Description

Unlock the potential of AI Agents in your Java applications by combining LangChain4j with Azure Container Apps (ACA) dynamic sessions connected to Azure AI services. This session showcases a practical example of building an agent capable of interacting with a remote environment, including file management. Learn how to define custom tools, integrate them into agent workflows, and leverage Azure's scalable infrastructure to deploy intelligent, dynamic solutions.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25337/>

---

## Event Name: Irresponsible AI Agents

## Description

Join us as we explore the potential risks of AI agents and tackle the challenge of embedding trustworthy AI practices into conversational AI platforms! This session dives deep into examples of irresponsible AI agents—showcasing jaw-dropping examples of model failures, adversarial jailbreaks, and other risks that erode trust and compliance.

We'll explore Microsoft’s cutting-edge tools for trustworthy AI, including content filters, red teaming strategies, and evaluations—featuring live demos of AI agents behaving both responsibly and irresponsibly in ways you won’t believe.

🔥 What you’ll walk away with:
✅ How to spot and mitigate AI risks before they can be exploited
✅ How to deploy Azure AI Content Safety to detect and mitigate risky behavior
✅ The secret sauce to making AI agents trustworthy

Get ready for a session packed with hype, high-stakes AI drama, and must-know strategies to keep your AI on the right side of history. Don’t just build AI—build AI that matters!

## URL
<https://developer.microsoft.com/en-us/reactor/events/25388/>

---

## Event Name: Build your code-first app with Azure AI Agent Service (.NET)

## Description

The Azure AI Agent Service is a seamless blend of service and SDK that simplifies the development of robust AI-driven solutions. In this session, you'll learn how to build your own code-first AI agent with Azure and C# that can answer questions, perform data analysis, and integrate external data sources. You'll also explore more complex architectures, including multiple agents working together.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25370/>

---

## Event Name: AI Agents + .NET Aspire

## Description

In this session we will share some of the most exciting developments on the .NET platform around Agents. Discover the current status of .NET, including its new features and enhancements. Explore the powerful AI Agent capabilities. And we will do some live coding with Agents and.NET Aspire.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25332/>

---

## Event Name: Semantic Kernel with C# to build multi-agent AI applications powered by Azure Cosmos

## Description

We will walk you through a multi-agent application in C# that is built on top of the Semantic Kernel framework. You will understand the concepts behind agentic applications, understand the implementation details and nuances, and learn how to integrate Azure Cosmos DB as the database for various use-cases.

## URL
<https://developer.microsoft.com/en-us/reactor/events/25455/>
