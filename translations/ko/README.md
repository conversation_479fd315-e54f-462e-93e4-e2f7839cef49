<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "09e975d95b470ee45ab546c22ee35d33",
  "translation_date": "2025-03-28T13:37:14+00:00",
  "source_file": "README.md",
  "language_code": "ko"
}
-->
# AI 에이전트 입문 - 강좌

![초보자를 위한 생성 AI](../../translated_images/repo-thumbnail.fdd5f487bb7274d4a08459d76907ec4914de268c99637e9af082b1d3eb0730e2.ko.png)

## AI 에이전트를 구축하기 위한 모든 기본 사항을 배우는 10개의 강의

[![GitHub license](https://img.shields.io/github/license/microsoft/ai-agents-for-beginners.svg)](https://github.com/microsoft/ai-agents-for-beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

### 언어 지원
[![English](https://img.shields.io/badge/English-brightgreen.svg?style=flat-square)](README.md)
[![Chinese Simplified](https://img.shields.io/badge/Chinese_Simplified-brightgreen.svg?style=flat-square)](../zh/README.md)
[![Chinese Traditional](https://img.shields.io/badge/Chinese_Traditional-brightgreen.svg?style=flat-square)](../tw/README.md)     
[![Chinese Hong Kong](https://img.shields.io/badge/Chinese_Hong_Kong-brightgreen.svg?style=flat-square)](../hk/README.md) 
[![French](https://img.shields.io/badge/French-brightgreen.svg?style=flat-square)](../fr/README.md)
[![Japanese](https://img.shields.io/badge/Japanese-brightgreen.svg?style=flat-square)](../ja/README.md) 
[![Korean](https://img.shields.io/badge/Korean-brightgreen.svg?style=flat-square)](./README.md)
[![Portuguese Brazilian](https://img.shields.io/badge/Portuguese_Brazilian-brightgreen.svg?style=flat-square)](../pt/README.md)
[![Spanish](https://img.shields.io/badge/Spanish-brightgreen.svg?style=flat-square)](../es/README.md)
[![German](https://img.shields.io/badge/German-brightgreen.svg?style=flat-square)](../de/README.md)  
[![Persian](https://img.shields.io/badge/Persian-brightgreen.svg?style=flat-square)](../fa/README.md) 
[![Polish](https://img.shields.io/badge/Polish-brightgreen.svg?style=flat-square)](../pl/README.md) 

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/ai-agents-for-beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/ai-agents-for-beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/ai-agents-for-beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/ai-agents-for-beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/ai-agents-for-beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/ai-agents-for-beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![Azure AI Discord](https://dcbadge.limes.pink/api/server/kzRShWzttr)](https://discord.gg/kzRShWzttr)


## 🌱 시작하기

이 강좌는 AI 에이전트를 구축하기 위한 기본 사항을 다루는 10개의 강의를 포함하고 있습니다. 각 강의는 독립적인 주제를 다루므로 원하는 곳에서 시작하세요!

이 강좌는 다국어 지원을 제공합니다. [지원되는 언어 목록](../..)을 확인하세요.

생성 AI 모델을 처음 사용해보는 경우, [초보자를 위한 생성 AI](https://aka.ms/genai-beginners) 강좌를 확인하세요. 이 강좌는 GenAI를 활용한 개발에 대한 21개의 강의를 포함하고 있습니다.

이 저장소를 [별(🌟)로 표시](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst)하거나 [포크](https://github.com/microsoft/ai-agents-for-beginners/fork)하여 코드를 실행하는 것을 잊지 마세요.

### 필요한 사항

이 강좌의 각 강의에는 코드 예제가 포함되어 있으며, code_samples 폴더에서 확인할 수 있습니다. 이 저장소를 [포크](https://github.com/microsoft/ai-agents-for-beginners/fork)하여 자신의 복사본을 생성하세요.  

이 강좌의 코드 예제는 Azure AI Foundry와 GitHub Model Catalog를 활용하여 언어 모델과 상호작용합니다:

- [Github Models](https://aka.ms/ai-agents-beginners/github-models) - 무료 / 제한적
- [Azure AI Foundry](https://aka.ms/ai-agents-beginners/ai-foundry) - Azure 계정 필요

이 강좌는 또한 Microsoft의 다음 AI 에이전트 프레임워크와 서비스를 사용합니다:

- [Azure AI Agent Service](https://aka.ms/ai-agents-beginners/ai-agent-service)
- [Semantic Kernel](https://aka.ms/ai-agents-beginners/semantic-kernel)  
- [AutoGen](https://aka.ms/ai-agents/autogen)  

이 과정의 코드를 실행하는 방법에 대한 자세한 내용은 [Course Setup](./00-course-setup/README.md)을 참조하세요.  

## 🙏 도움을 주고 싶으신가요?  

제안 사항이 있거나 철자 또는 코드 오류를 발견하셨나요? [문제 제기하기](https://github.com/microsoft/ai-agents-for-beginners/issues?WT.mc_id=academic-105485-koreyst) 또는 [풀 리퀘스트 생성하기](https://github.com/microsoft/ai-agents-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)를 통해 기여해주세요.  

AI 에이전트를 구축하는 과정에서 막히거나 질문이 있으시다면 [Azure AI Community Discord](https://discord.gg/kzRShWzttr)에 참여하세요.  

## 📂 각 레슨 포함 내용  

- README에 작성된 학습 자료와 짧은 동영상  
- Azure AI Foundry와 Github 모델을 지원하는 Python 코드 샘플 (무료 제공)  
- 학습을 계속할 수 있도록 추가 자료 링크 제공  

## 🗃️ 레슨  

| **레슨**                                 | **텍스트 & 코드**                                  | **동영상**                                                  | **추가 학습 자료**                                                                 |
|------------------------------------------|----------------------------------------------------|------------------------------------------------------------|------------------------------------------------------------------------------------|
| AI 에이전트 소개 및 활용 사례             | [Link](./01-intro-to-ai-agents/README.md)          | [Video](https://youtu.be/3zgm60bXmQk?si=z8QygFvYQv-9WtO1)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| AI 에이전트 프레임워크 탐구               | [Link](./02-explore-agentic-frameworks/README.md)  | [Video](https://youtu.be/ODwF-EZo_O8?si=Vawth4hzVaHv-u0H)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| AI 에이전트 디자인 패턴 이해              | [Link](./03-agentic-design-patterns/README.md)     | [Video](https://youtu.be/m9lM8qqoOEA?si=BIzHwzstTPL8o9GF)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 도구 활용 디자인 패턴                     | [Link](./04-tool-use/README.md)                    | [Video](https://youtu.be/vieRiPRx-gI?si=2z6O2Xu2cu_Jz46N)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 에이전트 RAG                              | [Link](./05-agentic-rag/README.md)                 | [Video](https://youtu.be/WcjAARvdL7I?si=gKPWsQpKiIlDH9A3)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 신뢰할 수 있는 AI 에이전트 구축           | [Link](./06-building-trustworthy-agents/README.md) | [Video](https://youtu.be/iZKkMEGBCUQ?si=jZjpiMnGFOE9L8OK ) | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 계획 디자인 패턴                          | [Link](./07-planning-design/README.md)             | [Video](https://youtu.be/kPfJ2BrBCMY?si=6SC_iv_E5-mzucnC)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 다중 에이전트 디자인 패턴                 | [Link](./08-multi-agent/README.md)                 | [Video](https://youtu.be/V6HpE9hZEx0?si=rMgDhEu7wXo2uo6g)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |  
| 메타인지 디자인 패턴                        | [Link](./09-metacognition/README.md)               | [Video](https://youtu.be/His9R6gw6Ec?si=8gck6vvdSNCt6OcF)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |
| 프로덕션에서의 AI 에이전트                  | [Link](./10-ai-agents-production/README.md)        | [Video](https://youtu.be/l4TP6IyJxmQ?si=31dnhexRo6yLRJDl)  | [Link](https://aka.ms/ai-agents-beginners/collection?WT.mc_id=academic-105485-koreyst) |

## 🌐 다국어 지원

| 언어                   | 코드 | 번역된 README 링크                                    | 마지막 업데이트 |
|------------------------|------|-----------------------------------------------------|-----------------|
| 중국어 (간체)          | zh   | [중국어 번역](../zh/README.md)          | 2025-03-24      |
| 중국어 (번체)          | tw   | [중국어 번역](../tw/README.md)          | 2025-03-28      |
| 중국어 (홍콩)          | hk   | [중국어 (홍콩) 번역](../hk/README.md)   | 2025-03-28      |
| 프랑스어               | fr   | [프랑스어 번역](../fr/README.md)        | 2025-03-28      |
| 일본어                 | ja   | [일본어 번역](../ja/README.md)          | 2025-03-28      |
| 한국어                 | ko   | [한국어 번역](./README.md)          | 2025-03-28      |
| 포르투갈어             | pt   | [포르투갈어 번역](../pt/README.md)      | 2025-03-28      |
| 스페인어               | es   | [스페인어 번역](../es/README.md)        | 2025-03-28      |
| 독일어                 | de   | [독일어 번역](../de/README.md)          | 2025-03-28      |
| 페르시아어             | fa   | [페르시아어 번역](../fa/README.md)      | 2025-03-28      |
| 폴란드어               | pl   | [폴란드어 번역](../pl/README.md)        | 2025-03-28      |

## 🎒 기타 강좌

우리 팀이 제작한 다른 강좌도 확인해 보세요!

- [**NEW** 초보자를 위한 .NET 기반 생성형 AI](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 생성형 AI](https://github.com/microsoft/generative-ai-for-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 머신러닝](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 데이터 과학](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 AI](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 사이버 보안](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [초보자를 위한 웹 개발](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 IoT](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 XR 개발](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI 페어 프로그래밍을 위한 GitHub Copilot 마스터하기](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET 개발자를 위한 GitHub Copilot 마스터하기](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [나만의 Copilot 어드벤처 선택하기](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

## 🌟 커뮤니티 감사

Agentic RAG를 시연하는 중요한 코드 샘플을 제공한 [Shivam Goyal](https://www.linkedin.com/in/shivam2003/) 님께 감사드립니다.

## 기여하기

이 프로젝트는 기여와 제안을 환영합니다. 대부분의 기여는 기여자 라이선스 계약(Contributor License Agreement, CLA)에 동의해야 하며, 이를 통해 귀하가 기여할 권리가 있고 실제로 기여를 허가한다는 것을 선언합니다. 자세한 내용은 <https://cla.opensource.microsoft.com>를 방문하세요.

Pull Request를 제출하면 CLA 봇이 자동으로 CLA 제공 여부를 확인하고 PR에 적절히 표시(예: 상태 확인, 댓글)를 추가합니다. 봇이 제공하는 지침을 따르기만 하면 됩니다. CLA는 모든 리포지토리에서 한 번만 제출하면 됩니다.
이 프로젝트는 [Microsoft 오픈 소스 행동 강령](https://opensource.microsoft.com/codeofconduct/)을 채택했습니다.  
자세한 내용은 [행동 강령 FAQ](https://opensource.microsoft.com/codeofconduct/faq/)를 참조하거나  
추가 질문이나 의견이 있는 경우 [<EMAIL>](mailto:<EMAIL>)으로 연락하세요.

## 상표

이 프로젝트는 프로젝트, 제품 또는 서비스와 관련된 상표나 로고를 포함할 수 있습니다. Microsoft 상표나 로고의 승인된 사용은  
[Microsoft의 상표 및 브랜드 가이드라인](https://www.microsoft.com/legal/intellectualproperty/trademarks/usage/general)을 준수해야 합니다.  
이 프로젝트의 수정된 버전에서 Microsoft 상표나 로고를 사용할 경우 혼란을 초래하거나 Microsoft의 후원을 암시해서는 안 됩니다.  
타사 상표나 로고의 사용은 해당 타사의 정책을 따라야 합니다.

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 노력하고 있으나, 자동 번역에는 오류나 부정확성이 포함될 수 있습니다. 원본 문서의 해당 언어 버전이 권위 있는 자료로 간주되어야 합니다. 중요한 정보에 대해서는 전문적인 인간 번역을 권장합니다. 이 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 책임을 지지 않습니다.