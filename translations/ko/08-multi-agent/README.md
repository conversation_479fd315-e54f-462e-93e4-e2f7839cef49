<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "bdb0a92e5a437d5fa985a6863f4a836c",
  "translation_date": "2025-03-28T13:49:16+00:00",
  "source_file": "08-multi-agent\\README.md",
  "language_code": "ko"
}
-->
[![Multi-Agent Design](../../../translated_images/lesson-8-thumbnail.1577e03e8b52f85e8e07afc1d72cb3a48680d14e23117bcf6ffbc521f956544f.ko.png)](https://youtu.be/kPfJ2BrBCMY?si=A7K44uMCqgvLQVCa)

> _(위 이미지를 클릭하면 이 강의의 영상을 볼 수 있습니다)_

# 다중 에이전트 설계 패턴

여러 에이전트를 포함하는 프로젝트를 시작하게 되면, 다중 에이전트 설계 패턴을 고려해야 합니다. 하지만 언제 다중 에이전트로 전환해야 하는지, 그리고 그것의 장점이 무엇인지 즉시 명확하지 않을 수 있습니다.

## 소개

이번 강의에서는 다음과 같은 질문에 답하고자 합니다:

- 다중 에이전트가 적용될 수 있는 시나리오는 무엇인가요?
- 하나의 에이전트가 여러 작업을 수행하는 것보다 다중 에이전트를 사용하는 장점은 무엇인가요?
- 다중 에이전트 설계 패턴을 구현하기 위한 구성 요소는 무엇인가요?
- 여러 에이전트가 서로 상호작용하는 방식을 어떻게 파악할 수 있을까요?

## 학습 목표

이 강의를 마친 후, 여러분은 다음을 할 수 있어야 합니다:

- 다중 에이전트가 적용될 수 있는 시나리오를 식별할 수 있다.
- 하나의 에이전트보다 다중 에이전트를 사용하는 장점을 인식할 수 있다.
- 다중 에이전트 설계 패턴을 구현하기 위한 구성 요소를 이해할 수 있다.

큰 그림은 무엇인가요?

*다중 에이전트는 여러 에이전트가 공동의 목표를 달성하기 위해 협력할 수 있도록 하는 설계 패턴입니다.*

이 패턴은 로봇공학, 자율 시스템, 분산 컴퓨팅 등 다양한 분야에서 널리 사용됩니다.

## 다중 에이전트가 적용될 수 있는 시나리오

그렇다면 어떤 시나리오가 다중 에이전트를 사용하는 데 적합할까요? 답은 여러 시나리오에서 다중 에이전트를 활용하는 것이 유익하다는 것입니다. 특히 다음과 같은 경우에 유용합니다:

- **큰 작업량**: 큰 작업량은 더 작은 작업으로 나누어 다양한 에이전트에 할당할 수 있습니다. 이를 통해 병렬 처리와 더 빠른 완료가 가능합니다. 예를 들어, 대규모 데이터 처리 작업이 이에 해당합니다.
- **복잡한 작업**: 큰 작업량과 마찬가지로 복잡한 작업도 더 작은 하위 작업으로 나누어 각 에이전트가 작업의 특정 측면을 전문적으로 처리할 수 있습니다. 자율 주행 차량의 경우, 각 에이전트가 내비게이션, 장애물 감지, 다른 차량과의 통신을 관리하는 좋은 예입니다.
- **다양한 전문성**: 서로 다른 에이전트가 다양한 전문성을 갖추고 있어 단일 에이전트보다 작업의 다양한 측면을 더 효과적으로 처리할 수 있습니다. 예를 들어, 의료 분야에서 에이전트가 진단, 치료 계획, 환자 모니터링을 관리하는 경우가 좋은 사례입니다.

## 하나의 에이전트보다 다중 에이전트를 사용하는 장점

단일 에이전트 시스템은 간단한 작업에는 잘 작동할 수 있지만, 더 복잡한 작업의 경우 다중 에이전트를 사용하는 것이 여러 가지 장점을 제공합니다:

- **전문화**: 각 에이전트가 특정 작업에 특화될 수 있습니다. 단일 에이전트가 전문화되지 않은 경우, 모든 작업을 처리할 수는 있지만 복잡한 작업에 직면했을 때 무엇을 해야 할지 혼란스러워할 수 있습니다. 예를 들어, 적합하지 않은 작업을 수행하게 될 수도 있습니다.
- **확장성**: 시스템을 확장할 때 단일 에이전트에 과부하를 주는 대신 더 많은 에이전트를 추가하는 것이 더 쉽습니다.
- **장애 허용성**: 하나의 에이전트가 실패하더라도 다른 에이전트는 계속 작동하여 시스템의 신뢰성을 보장할 수 있습니다.

예를 들어, 사용자를 위한 여행 예약 시스템을 생각해봅시다. 단일 에이전트 시스템은 항공편 검색, 호텔 예약, 렌터카 예약 등 여행 예약 프로세스의 모든 측면을 처리해야 합니다. 이를 단일 에이전트로 구현하려면 해당 작업을 처리할 도구가 필요하며, 이는 유지 관리와 확장이 어려운 복잡하고 단일 구조의 시스템으로 이어질 수 있습니다. 반면, 다중 에이전트 시스템은 항공편 검색, 호텔 예약, 렌터카 예약에 특화된 다양한 에이전트를 가질 수 있습니다. 이는 시스템을 더 모듈화하고, 유지 관리가 용이하며 확장 가능하게 만듭니다.

이것을 소규모 가족 운영 여행사와 프랜차이즈 여행사를 비교해보세요. 가족 운영 여행사는 단일 에이전트가 여행 예약 프로세스의 모든 측면을 처리하는 반면, 프랜차이즈 여행사는 다양한 에이전트가 각기 다른 측면을 처리합니다.

## 다중 에이전트 설계 패턴 구현을 위한 구성 요소

다중 에이전트 설계 패턴을 구현하기 전에, 패턴을 구성하는 요소를 이해해야 합니다.

다시 사용자의 여행 예약 예제를 살펴보겠습니다. 이 경우, 구성 요소는 다음과 같습니다:

- **에이전트 간 통신**: 항공편 검색, 호텔 예약, 렌터카 예약 에이전트는 사용자의 선호도와 제약 조건에 대한 정보를 공유하고 소통해야 합니다. 이를 위한 프로토콜과 방법을 결정해야 합니다. 구체적으로, 항공편 검색 에이전트는 호텔 예약 에이전트와 소통하여 호텔이 항공편 날짜와 동일한 날짜로 예약되도록 해야 합니다. 즉, *어떤 에이전트가 정보를 공유하고, 어떻게 공유하는지*를 결정해야 합니다.
- **조정 메커니즘**: 에이전트는 사용자의 선호도와 제약 조건이 충족되도록 작업을 조정해야 합니다. 사용자가 공항 근처의 호텔을 선호할 수 있고, 렌터카가 공항에서만 이용 가능하다는 제약 조건이 있을 수 있습니다. 이는 호텔 예약 에이전트가 렌터카 예약 에이전트와 협력하여 사용자의 선호도와 제약 조건을 충족해야 함을 의미합니다. 따라서 *에이전트가 어떻게 행동을 조정할 것인지*를 결정해야 합니다.
- **에이전트 아키텍처**: 에이전트는 사용자와의 상호작용에서 결정을 내리고 학습할 내부 구조를 가져야 합니다. 예를 들어, 항공편 검색 에이전트는 사용자에게 추천할 항공편을 결정할 내부 구조가 필요합니다. 이는 *에이전트가 어떻게 결정을 내리고 학습하는지*를 결정해야 함을 의미합니다. 예를 들어, 항공편 검색 에이전트는 사용자의 과거 선호도를 기반으로 항공편을 추천하기 위해 기계 학습 모델을 사용할 수 있습니다.
- **다중 에이전트 상호작용 가시성**: 여러 에이전트가 서로 어떻게 상호작용하는지에 대한 가시성을 확보해야 합니다. 이를 위해 에이전트 활동과 상호작용을 추적하기 위한 도구와 기술이 필요합니다. 예를 들어, 로깅 및 모니터링 도구, 시각화 도구, 성능 지표 등이 이에 포함될 수 있습니다.
- **다중 에이전트 패턴**: 다중 에이전트 시스템을 구현하는 데는 중앙 집중식, 분산형, 하이브리드 아키텍처와 같은 다양한 패턴이 있습니다. 자신의 사용 사례에 가장 적합한 패턴을 결정해야 합니다.
- **사람의 개입**: 대부분의 경우, 사람의 개입이 필요하며 에이전트가 언제 인간의 개입을 요청해야 하는지 지시해야 합니다. 예를 들어, 사용자가 에이전트가 추천하지 않은 특정 호텔이나 항공편을 요청하거나, 항공편이나 호텔을 예약하기 전에 확인을 요청할 수 있습니다.

## 다중 에이전트 상호작용 가시성

여러 에이전트가 서로 어떻게 상호작용하는지에 대한 가시성을 확보하는 것은 매우 중요합니다. 이러한 가시성은 디버깅, 최적화, 전체 시스템의 효과성을 보장하는 데 필수적입니다. 이를 위해 에이전트 활동과 상호작용을 추적하기 위한 도구와 기술이 필요합니다. 예를 들어, 로깅 및 모니터링 도구, 시각화 도구, 성능 지표 등이 이에 포함될 수 있습니다.

예를 들어, 사용자의 여행 예약을 처리하는 경우, 각 에이전트의 상태, 사용자의 선호도와 제약 조건, 에이전트 간의 상호작용을 보여주는 대시보드를 가질 수 있습니다. 이 대시보드는 사용자의 여행 날짜, 항공편 에이전트가 추천한 항공편, 호텔 에이전트가 추천한 호텔, 렌터카 에이전트가 추천한 렌터카를 보여줄 수 있습니다. 이를 통해 에이전트가 서로 어떻게 상호작용하고 있는지, 사용자의 선호도와 제약 조건이 충족되고 있는지를 명확히 파악할 수 있습니다.

다음은 이러한 측면을 더 자세히 살펴본 내용입니다:

- **로깅 및 모니터링 도구**: 각 에이전트가 수행한 작업에 대해 로깅을 해야 합니다. 로그 항목에는 작업을 수행한 에이전트, 수행된 작업, 작업이 수행된 시간, 작업 결과에 대한 정보가 포함될 수 있습니다. 이러한 정보는 디버깅, 최적화 등에 활용될 수 있습니다.
- **시각화 도구**: 시각화 도구는 에이전트 간의 상호작용을 보다 직관적으로 볼 수 있도록 도와줍니다. 예를 들어, 에이전트 간 정보 흐름을 보여주는 그래프를 만들 수 있습니다. 이를 통해 병목 현상, 비효율성, 기타 시스템 문제를 식별할 수 있습니다.
- **성능 지표**: 성능 지표는 다중 에이전트 시스템의 효과성을 추적하는 데 도움이 됩니다. 예를 들어, 작업 완료 시간, 단위 시간당 완료된 작업 수, 에이전트가 제공한 추천의 정확도를 추적할 수 있습니다. 이러한 정보는 개선 영역을 식별하고 시스템을 최적화하는 데 유용합니다.

## 다중 에이전트 패턴

다중 에이전트 애플리케이션을 생성하는 데 사용할 수 있는 몇 가지 구체적인 패턴을 살펴보겠습니다. 다음은 고려할 만한 흥미로운 패턴입니다:

### 그룹 채팅

이 패턴은 여러 에이전트가 서로 소통할 수 있는 그룹 채팅 애플리케이션을 만들고자 할 때 유용합니다. 이 패턴의 일반적인 사용 사례로는 팀 협업, 고객 지원, 소셜 네트워킹 등이 있습니다.

이 패턴에서 각 에이전트는 그룹 채팅의 사용자로 표현되며, 메시지는 메시징 프로토콜을 사용하여 에이전트 간에 교환됩니다. 에이전트는 그룹 채팅에 메시지를 보내고, 그룹 채팅에서 메시지를 수신하며, 다른 에이전트의 메시지에 응답할 수 있습니다.

이 패턴은 모든 메시지가 중앙 서버를 통해 라우팅되는 중앙 집중식 아키텍처 또는 메시지가 직접 교환되는 분산형 아키텍처를 사용하여 구현할 수 있습니다.

![그룹 채팅](../../../translated_images/multi-agent-group-chat.82d537c5c8dc833abbd252033e60874bc9d00df7193888b3377f8426449a0b20.ko.png)

### 작업 이관

이 패턴은 여러 에이전트가 서로 작업을 이관할 수 있는 애플리케이션을 만들고자 할 때 유용합니다.

이 패턴의 일반적인 사용 사례로는 고객 지원, 작업 관리, 워크플로 자동화 등이 있습니다.

이 패턴에서 각 에이전트는 작업 또는 워크플로의 단계를 나타내며, 에이전트는 사전 정의된 규칙에 따라 작업을 다른 에이전트에 이관할 수 있습니다.

![작업 이관](../../../translated_images/multi-agent-hand-off.ed4f0a5a58614a8a3e962fc476187e630a3ba309d066e460f017b503d0b84cfc.ko.png)

### 협력 필터링

이 패턴은 여러 에이전트가 협력하여 사용자에게 추천을 제공할 수 있는 애플리케이션을 만들고자 할 때 유용합니다.

여러 에이전트가 협력하는 이유는 각 에이전트가 다른 전문성을 가지고 있어 추천 과정에서 서로 다른 방식으로 기여할 수 있기 때문입니다.

예를 들어, 사용자가 주식 시장에서 가장 좋은 주식을 추천받고자 한다고 가정해봅시다.

- **산업 전문가**: 한 에이전트는 특정 산업에 대한 전문가일 수 있습니다.
- **기술적 분석**: 또 다른 에이전트는 기술적 분석 전문가일 수 있습니다.
- **기본적 분석**: 또 다른 에이전트는 기본적 분석 전문가일 수 있습니다. 이러한 에이전트들이 협력함으로써 사용자에게 더 포괄적인 추천을 제공할 수 있습니다.

![추천](../../../translated_images/multi-agent-filtering.719217d169391ddb118bbb726b19d4d89ee139f960f8749ccb2400efb4d0ce79.ko.png)

## 시나리오: 환불 프로세스

고객이 제품 환불을 요청하는 시나리오를 고려해봅시다. 이 과정에는 여러 에이전트가 관여할 수 있지만, 이를 환불 프로세스에 특화된 에이전트와 비즈니스의 다른 프로세스에서도 사용할 수 있는 일반적인 에이전트로 나눌 수 있습니다.

**환불 프로세스에 특화된 에이전트**:

다음은 환불 프로세스에 관여할 수 있는 에이전트들입니다:

- **고객 에이전트**: 고객을 대표하며 환불 프로세스를 시작하는 역할을 합니다.
- **판매자 에이전트**: 판매자를 대표하며 환불을 처리하는 역할을 합니다.
- **결제 에이전트**: 결제 과정을 대표하며 고객의 결제를 환불하는 역할을 합니다.
- **해결 에이전트**: 해결 과정을 대표하며 환불 과정에서 발생하는 문제를 해결하는 역할을 합니다.
- **준수 에이전트**: 규정 및 정책 준수를 보장하는 역할을 합니다.

**일반적인 에이전트**:

이 에이전트들은 비즈니스의 다른 부분에서도 사용할 수 있습니다.

- **배송 에이전트**: 배송 과정을 대표하며 제품을 판매자에게 다시 배송하는 역할을 합니다. 이 에이전트는 환불 프로세스뿐만 아니라 일반적인 제품 배송에도 사용할 수 있습니다.
- **피드백 에이전트**: 피드백 과정을 대표하며 고객의 피드백을 수집하는 역할을 합니다. 피드백은 환불 과정뿐만 아니라 언제든지 수집될 수 있습니다.
- **에스컬레이션 에이전트**: 에스컬레이션 과정을 대표하며 문제를 상위 지원 수준으로 전달하는 역할을 합니다. 이 에이전트는 문제를 에스컬레이션해야 하는 모든 프로세스에서 사용할 수 있습니다.
- **알림 에이전트**: 알림 과정을 대표하며 환불 프로세스의 여러 단계에서 고객에게 알림을 보내는 역할을 합니다.
- **분석 에이전트**: 분석 과정을 대표하며 환불 프로세스와 관련된 데이터를 분석하는 역할을 합니다.
- **감사 에이전트**: 감사 과정을 대표하며 환불 프로세스가 올바르게 수행되고 있는지 감사하는 역할을 합니다.
- **보고 에이전트**: 보고 과정을 대표하며 환불 프로세스에 대한 보고서를 생성하는 역할을 합니다.
- **지식 에이전트**: 지식 과정을 대표하며 환불 프로세스와 관련된 정보를 유지 관리하는 역할을 합니다. 이 에이전트는 환불뿐만 아니라 비즈니스의 다른 부분에 대해서도 지식을 보유할 수 있습니다.
- **보안 에이전트**: 보안 과정을 대표하며 환불 프로세스의 보안을 보장하는 역할을 합니다.
- **품질 에이전트**: 품질 과정을 대표하며 환불 프로세스의 품질을 보장하는 역할을 합니다.

위에서 언급한 에이전트들은 환불 프로세스에 특화된 에이전트와 비즈니스의 다른 부분에서 사용할 수 있는 일반적인 에이전트를 포함하여 매우 다양합니다. 이를 통해 다중 에이전트 시스템에서 사용할 에이전트를 결정하는 방법에 대한 아이디어를 얻을 수 있기를 바랍니다.

## 과제

고객 지원 프로세스를 위한 다중 에이전트 시스템을 설계하세요. 프로세스에 관여하는 에이전트, 그들의 역할과 책임, 상호작용 방식을 식별하세요. 고객 지원 프로세스에 특화된 에이전트와 비즈니스의 다른 부분에서도 사용할 수 있는 일반적인 에이전트를 모두 고려하세요.

> 답을 읽기 전에 생각해 보세요. 예상보다 더 많은 에이전트가 필요할 수도 있습니다.

> TIP: 고객 지원 프로세스의 다양한 단계를 생각하고, 시스템에 필요한 에이전트도 고려하세요.

## 솔루션

[솔루션](./solution/solution.md)

## 지식 점검

질문: 언제 다중 에이전트를 사용하는 것을 고려해야 하나요?

- [ ] A1: 작업량이 적고 작업이 간단할 때
- [ ] A2: 작업량이 많을 때
- [ ] A3: 작업이 간단할 때

[퀴즈 솔루션](./solution/solution-quiz.md)

## 요약

이번 강의에서는 다중 에이전트 설계 패턴에 대해 살펴보았습니다. 다중 에이전트가 적용될 수 있는 시나리오, 하나의 에이전트보다 다중 에이전트를 사용하는 장점, 다중 에이전트 설계 패턴을 구현하기 위한 구성 요소, 그리고 여러 에이전트가 서로 상호작용하는 방식을 파악하는 방법을 다뤘습니다.

## 추가 자료

- ##

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 노력하고 있으나, 자동 번역은 오류나 부정확한 내용이 포함될 수 있습니다. 원본 문서(원어로 작성된 문서)가 권위 있는 출처로 간주되어야 합니다. 중요한 정보의 경우, 전문적인 인간 번역을 권장합니다. 이 번역을 사용하여 발생하는 오해나 잘못된 해석에 대해 책임을 지지 않습니다.