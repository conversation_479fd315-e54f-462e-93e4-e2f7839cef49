<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "d38387460faaff27512a6b8c91ba9737",
  "translation_date": "2025-03-28T13:57:21+00:00",
  "source_file": "08-multi-agent\\solution\\solution.md",
  "language_code": "ko"
}
-->
**고객 지원 프로세스를 위한 특정 에이전트**:

- **Customer agent**: 이 에이전트는 고객을 대표하며 지원 프로세스를 시작하는 역할을 담당합니다.
- **Support agent**: 이 에이전트는 지원 프로세스를 대표하며 고객에게 도움을 제공하는 역할을 담당합니다.
- **Escalation agent**: 이 에이전트는 문제를 상위 지원 단계로 이관하는 역할을 담당하며 이관 프로세스를 대표합니다.
- **Resolution agent**: 이 에이전트는 지원 프로세스 중 발생하는 문제를 해결하는 역할을 담당하며 해결 프로세스를 대표합니다.
- **Feedback agent**: 이 에이전트는 고객으로부터 피드백을 수집하는 역할을 담당하며 피드백 프로세스를 대표합니다.
- **Notification agent**: 이 에이전트는 지원 프로세스의 다양한 단계에서 고객에게 알림을 보내는 역할을 담당하며 알림 프로세스를 대표합니다.
- **Analytics agent**: 이 에이전트는 지원 프로세스와 관련된 데이터를 분석하는 역할을 담당하며 분석 프로세스를 대표합니다.
- **Audit agent**: 이 에이전트는 지원 프로세스가 올바르게 수행되고 있는지 감사하는 역할을 담당하며 감사 프로세스를 대표합니다.
- **Reporting agent**: 이 에이전트는 지원 프로세스에 대한 보고서를 생성하는 역할을 담당하며 보고 프로세스를 대표합니다.
- **Knowledge agent**: 이 에이전트는 지원 프로세스와 관련된 정보의 지식 베이스를 유지하는 역할을 담당하며 지식 프로세스를 대표합니다.
- **Security agent**: 이 에이전트는 지원 프로세스의 보안을 보장하는 역할을 담당하며 보안 프로세스를 대표합니다.
- **Quality agent**: 이 에이전트는 지원 프로세스의 품질을 보장하는 역할을 담당하며 품질 프로세스를 대표합니다.
- **Compliance agent**: 이 에이전트는 지원 프로세스가 규정 및 정책을 준수하도록 보장하는 역할을 담당하며 준수 프로세스를 대표합니다.
- **Training agent**: 이 에이전트는 고객을 지원하는 방법에 대해 지원 에이전트를 교육하는 역할을 담당하며 교육 프로세스를 대표합니다.

이 정도 에이전트라면 예상보다 많았나요, 아니면 적었나요?

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 노력하고 있지만, 자동 번역에는 오류나 부정확성이 포함될 수 있습니다. 원문이 작성된 언어의 문서를 신뢰할 수 있는 권위 있는 자료로 간주해야 합니다. 중요한 정보의 경우, 전문 번역가에 의한 번역을 권장합니다. 이 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.  