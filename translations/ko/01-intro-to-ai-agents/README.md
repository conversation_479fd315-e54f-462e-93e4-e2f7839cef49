<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "233e7a18025a27eae95b653e9b5b5aa5",
  "translation_date": "2025-03-28T13:40:48+00:00",
  "source_file": "01-intro-to-ai-agents\\README.md",
  "language_code": "ko"
}
-->
[![Intro to AI Agents](../../../translated_images/lesson-1-thumbnail.062500b037054b99431a123879d8c1f55899d942cfc286f105e8c5ce417fe51e.ko.png)](https://youtu.be/3zgm60bXmQk?si=QA4CW2-cmul5kk3D)

> _(위 이미지를 클릭하면 이 강의의 영상을 볼 수 있습니다)_

# AI 에이전트와 에이전트 활용 사례 소개

"초보자를 위한 AI 에이전트" 강좌에 오신 것을 환영합니다! 이 강좌는 AI 에이전트를 구축하기 위한 기본 지식과 실용적인 예제를 제공합니다.

다른 학습자와 AI 에이전트 개발자를 만나고, 강좌와 관련된 질문을 할 수 있습니다.

이 강좌를 시작하기 위해 먼저 AI 에이전트가 무엇인지, 우리가 구축하는 애플리케이션과 워크플로우에서 이를 어떻게 활용할 수 있는지에 대해 이해해 보겠습니다.

## 소개

이 강의에서는 다음 내용을 다룹니다:

- AI 에이전트란 무엇이며, 에이전트의 종류는 어떤 것이 있는가?
- AI 에이전트에 적합한 활용 사례는 무엇이며, 이를 통해 어떻게 도움을 받을 수 있는가?
- 에이전트 솔루션을 설계할 때 기본적으로 필요한 요소는 무엇인가?

## 학습 목표
이 강의를 완료한 후에는 다음을 할 수 있어야 합니다:

- AI 에이전트 개념을 이해하고, 다른 AI 솔루션과의 차이를 구분할 수 있다.
- AI 에이전트를 효율적으로 적용할 수 있다.
- 사용자와 고객 모두를 위해 생산적으로 에이전트 솔루션을 설계할 수 있다.

## AI 에이전트 정의 및 종류

### AI 에이전트란 무엇인가?

AI 에이전트는 **대규모 언어 모델(LLM)**이 **도구와 지식**에 접근하여 **행동을 수행**할 수 있도록 기능을 확장하는 **시스템**입니다.

이 정의를 더 작은 구성 요소로 나누어 살펴보겠습니다:

- **시스템** - 에이전트를 단일 구성 요소로 생각하는 것이 아니라 여러 구성 요소로 이루어진 시스템으로 보는 것이 중요합니다. AI 에이전트의 기본 구성 요소는 다음과 같습니다:
  - **환경** - AI 에이전트가 운영하는 정의된 공간입니다. 예를 들어, 여행 예약 AI 에이전트를 생각해보면, 환경은 AI 에이전트가 작업을 완료하기 위해 사용하는 여행 예약 시스템이 될 수 있습니다.
  - **센서** - 환경은 정보를 제공하고 피드백을 제공합니다. AI 에이전트는 센서를 사용하여 환경의 현재 상태에 대한 정보를 수집하고 해석합니다. 여행 예약 에이전트의 경우, 호텔 이용 가능 여부나 항공편 가격과 같은 정보를 제공받을 수 있습니다.
  - **액추에이터** - AI 에이전트가 환경의 현재 상태를 받으면, 해당 작업을 수행하기 위해 환경을 변경하는 행동을 결정합니다. 여행 예약 에이전트의 경우, 사용자를 위해 이용 가능한 객실을 예약하는 행동을 수행할 수 있습니다.

![AI 에이전트란 무엇인가?](../../../translated_images/what-are-ai-agents.125520f55950b252a429b04a9f41e0152d4dafa1f1bd9081f4f574631acb759e.ko.png)

**대규모 언어 모델** - 에이전트의 개념은 LLM이 생성되기 이전부터 존재했습니다. LLM을 사용하여 AI 에이전트를 구축하는 장점은 인간의 언어와 데이터를 해석할 수 있는 능력입니다. 이러한 능력은 LLM이 환경 정보를 해석하고 환경을 변경하기 위한 계획을 정의할 수 있게 합니다.

**행동 수행** - AI 에이전트 시스템 외부에서 LLM은 사용자의 프롬프트를 기반으로 콘텐츠 또는 정보를 생성하는 행동으로 제한됩니다. 그러나 AI 에이전트 시스템 내에서는 사용자의 요청을 해석하고 환경에서 사용할 수 있는 도구를 사용하여 작업을 수행할 수 있습니다.

**도구 접근** - LLM이 접근할 수 있는 도구는 1) 운영하는 환경과 2) AI 에이전트를 개발한 개발자에 의해 정의됩니다. 여행 에이전트의 예를 들어보면, 에이전트의 도구는 예약 시스템에서 사용할 수 있는 작업으로 제한되거나, 개발자가 에이전트의 도구 접근을 항공편으로 제한할 수 있습니다.

**지식** - 환경에서 제공되는 정보 외에도, AI 에이전트는 다른 시스템, 서비스, 도구, 심지어 다른 에이전트로부터 지식을 얻을 수 있습니다. 여행 에이전트의 경우, 이 지식은 고객 데이터베이스에 있는 사용자의 여행 선호 정보가 될 수 있습니다.

### 다양한 에이전트 유형

이제 AI 에이전트의 일반적인 정의를 살펴보았으니, 특정 에이전트 유형과 이를 여행 예약 AI 에이전트에 적용하는 방법을 살펴보겠습니다.

| **에이전트 유형**               | **설명**                                                                                                                       | **예시**                                                                                                                                                                                                                   |
| ----------------------------- | ----------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **단순 반사형 에이전트**       | 미리 정의된 규칙에 따라 즉각적인 행동을 수행합니다.                                                                               | 여행 에이전트가 이메일의 내용을 해석하고 여행 관련 불만을 고객 서비스로 전달합니다.                                                                                                                          |
| **모델 기반 반사형 에이전트**  | 세계의 모델과 모델의 변화에 따라 행동을 수행합니다.                                                                              | 여행 에이전트가 과거 가격 데이터를 기반으로 가격 변화가 큰 경로를 우선적으로 추천합니다.                                                                                                             |
| **목표 기반 에이전트**         | 목표를 해석하고 목표를 달성하기 위한 행동을 결정하여 계획을 수립합니다.                                                           | 여행 에이전트가 현재 위치에서 목적지까지 필요한 여행 준비(자동차, 대중교통, 항공편)를 결정하여 여행을 예약합니다.                                                                                |
| **효용 기반 에이전트**         | 선호도를 고려하고 수치적으로 트레이드오프를 평가하여 목표를 달성하는 방법을 결정합니다.                                           | 여행 에이전트가 편리함과 비용을 비교하여 최적의 효용을 제공하는 여행을 예약합니다.                                                                                                                                          |
| **학습 에이전트**              | 피드백에 따라 행동을 조정하며 시간이 지남에 따라 개선됩니다.                                                                      | 여행 에이전트가 고객의 여행 후 설문조사 피드백을 사용하여 미래 예약에 대한 조정을 통해 개선합니다.                                                                                                               |
| **계층형 에이전트**            | 여러 에이전트를 계층 시스템으로 구성하여 상위 레벨 에이전트가 작업을 하위 레벨 에이전트가 완료할 수 있는 하위 작업으로 나눕니다. | 여행 에이전트가 여행을 취소할 때 작업을 세부 작업(예: 특정 예약 취소)으로 나누고, 하위 레벨 에이전트가 이를 완료한 후 상위 레벨 에이전트에 보고합니다.                                     |
| **다중 에이전트 시스템(MAS)**  | 에이전트가 협력적 또는 경쟁적으로 독립적으로 작업을 완료합니다.                                                                  | 협력적: 여러 에이전트가 호텔, 항공편, 엔터테인먼트 등 특정 여행 서비스를 예약합니다. 경쟁적: 여러 에이전트가 고객을 호텔에 예약하기 위해 공유 호텔 예약 캘린더를 관리하며 경쟁합니다. |

## AI 에이전트를 사용하는 경우

앞서 여행 에이전트 사례를 사용하여 다양한 에이전트 유형이 여행 예약의 다양한 시나리오에서 어떻게 사용될 수 있는지 설명했습니다. 이 애플리케이션은 강좌 내내 계속 사용할 것입니다.

AI 에이전트가 가장 적합한 활용 사례의 유형을 살펴보겠습니다:

![AI 에이전트를 사용하는 경우](../../../translated_images/when-to-use-ai-agents.912b9a02e9e0e2af45a3e24faa4e912e334ec23f21f0cf5cb040b7e899b09cd0.ko.png)

- **개방형 문제** - 워크플로우에 항상 하드코딩할 수 없는 작업을 완료하기 위해 LLM이 필요한 단계를 결정하도록 허용합니다.
- **다단계 프로세스** - 단일 조회가 아닌 여러 단계에 걸쳐 도구나 정보를 사용해야 하는 복잡한 작업에 적합합니다.
- **시간 경과에 따른 개선** - 환경 또는 사용자로부터 피드백을 받아 더 나은 효용을 제공하기 위해 시간이 지남에 따라 개선할 수 있는 작업에 적합합니다.

AI 에이전트를 사용하는 데 있어 추가적인 고려 사항은 "신뢰할 수 있는 AI 에이전트 구축" 강의에서 다룹니다.

## 에이전트 솔루션의 기본

### 에이전트 개발

AI 에이전트 시스템을 설계하는 첫 번째 단계는 도구, 행동, 그리고 행동 방식을 정의하는 것입니다. 이 강좌에서는 **Azure AI Agent Service**를 사용하여 에이전트를 정의하는 데 초점을 맞춥니다. 이 서비스는 다음과 같은 기능을 제공합니다:

- OpenAI, Mistral, Llama와 같은 오픈 모델 선택
- Tripadvisor와 같은 제공자를 통한 라이선스 데이터 사용
- 표준화된 OpenAPI 3.0 도구 사용

### 에이전트 패턴

LLM과의 소통은 프롬프트를 통해 이루어집니다. AI 에이전트의 준자율적인 특성상, 환경 변화 후에 LLM을 수동으로 다시 프롬프트하는 것이 항상 가능하거나 필요한 것은 아닙니다. 우리는 LLM을 보다 확장 가능한 방식으로 여러 단계에 걸쳐 프롬프트할 수 있는 **에이전트 패턴**을 사용합니다.

이 강좌는 현재 인기 있는 에이전트 패턴 중 일부로 나뉘어 있습니다.

### 에이전트 프레임워크

에이전트 프레임워크는 개발자가 코드로 에이전트 패턴을 구현할 수 있게 합니다. 이 프레임워크는 템플릿, 플러그인, 도구를 제공하여 더 나은 AI 에이전트 협업을 가능하게 합니다. 이를 통해 AI 에이전트 시스템의 가시성과 문제 해결 능력을 향상시킬 수 있습니다.

이 강좌에서는 연구 중심의 AutoGen 프레임워크와 Semantic Kernel의 프로덕션 준비된 에이전트 프레임워크를 탐구할 것입니다.

## 이전 강의

[강좌 설정](../00-course-setup/README.md)

## 다음 강의

[에이전트 프레임워크 탐구](../02-explore-agentic-frameworks/README.md)

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 최선을 다하고 있지만, 자동 번역에는 오류나 부정확성이 포함될 수 있습니다. 원문이 작성된 언어로 된 원본 문서를 신뢰할 수 있는 권위 있는 자료로 간주해야 합니다. 중요한 정보에 대해서는 전문적인 인간 번역을 권장합니다. 이 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.