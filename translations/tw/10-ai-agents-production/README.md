<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "44013a98d980c8b92d4b814dc49b545d",
  "translation_date": "2025-03-28T14:21:07+00:00",
  "source_file": "10-ai-agents-production\\README.md",
  "language_code": "tw"
}
-->
[![AI Agents In Production](../../../translated_images/lesson-10-thumbnail.0b68f4240618b3d5b26693b78cf2cf0a8b36131b50bb08daf91d548cecc87424.tw.png)](https://youtu.be/l4TP6IyJxmQ?si=IvCW3cbw0NJ2mUMV)

> _(點擊上方圖片觀看本課程影片)_
# AI代理在實際應用中的部署

## 介紹

本課程將涵蓋：

- 如何有效規劃將AI代理部署到生產環境。
- 部署AI代理到生產環境時可能面臨的常見錯誤與問題。
- 如何在維持AI代理性能的同時管理成本。

## 學習目標

完成本課程後，你將學會或理解：

- 提升AI代理系統性能、成本效益及效果的技巧。
- 評估AI代理的方式與內容。
- 部署AI代理到生產環境時如何控制成本。

部署值得信賴的AI代理非常重要。請參考「建立值得信賴的AI代理」課程。

## 評估AI代理

在部署AI代理之前、期間以及之後，建立一套完善的評估系統至關重要。這能確保你的系統符合你以及使用者的目標。

評估AI代理時，除了代理的輸出，還需評估整個AI代理運行的系統。這包括但不限於：

- 初始模型請求。
- 代理識別使用者意圖的能力。
- 代理識別執行任務所需工具的能力。
- 工具對代理請求的響應。
- 代理解釋工具響應的能力。
- 使用者對代理響應的回饋。

這樣可以更模組化地識別需要改進的地方。接著，你能更高效地監控模型、提示、工具及其他元件的變更效果。

## AI代理常見問題及可能解決方案

| **問題**                                      | **可能解決方案**                                                                                                                                                                                                     |
| ---------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| AI代理執行任務不一致                          | - 優化提供給AI代理的提示，明確目標。<br>- 確定是否可以將任務拆分為子任務並由多個代理分別處理。                                                                                                                      |
| AI代理陷入無限循環                            | - 確保設置明確的終止條件，讓代理知道何時停止過程。<br>- 對需要推理和規劃的複雜任務，使用專門針對推理任務的大型模型。                                                                                                 |
| AI代理工具調用表現不佳                        | - 在代理系統外測試並驗證工具的輸出。<br>- 優化工具的參數設定、提示及命名。                                                                                                                                            |
| 多代理系統表現不一致                          | - 優化提供給每個代理的提示，確保它們具體且相互區分。<br>- 建立分層系統，使用「路由」或控制代理來判斷哪個代理是合適的。                                                                                              |

## 成本管理

以下是管理AI代理部署成本的一些策略：

- **緩存響應** - 識別常見請求和任務，並在進入代理系統之前提供響應，是減少類似請求數量的好方法。你甚至可以實施一個流程，使用更基礎的AI模型來判斷請求與緩存請求的相似度。

- **使用較小的模型** - 小型語言模型（SLM）在某些代理用例中表現良好，並能顯著降低成本。如前所述，建立評估系統以判定並比較其性能與大型模型的差異，是理解SLM在你的用例中表現的最佳方式。

- **使用路由模型** - 類似的策略是使用多樣化的模型和尺寸。你可以使用LLM/SLM或無伺服器函數，根據任務的複雜程度將請求路由至最適合的模型。這能在確保性能的同時降低成本。

## 恭喜

這是「AI代理入門」的最後一課。

我們計劃根據反饋和這個不斷發展的行業的變化繼續新增課程，因此請隨時回來查看。

如果你想繼續學習並建立AI代理，請加入<a href="https://discord.gg/kzRShWzttr" target="_blank">Azure AI Community Discord</a>。

我們在那裡舉辦工作坊、社群圓桌會議以及「隨便問我什麼」的活動。

我們還有一系列學習資源，可以幫助你開始在生產環境中建立AI代理。

## 前一課程

[元認知設計模式](../09-metacognition/README.md)

**免責聲明**：  
本文檔使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們努力確保翻譯的準確性，但請注意，自動翻譯可能包含錯誤或不準確之處。應以原文檔的原始語言版本作為權威來源。對於關鍵資訊，建議尋求專業人工翻譯。我們對因使用此翻譯而產生的任何誤解或誤讀概不負責。