<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T14:05:10+00:00",
  "source_file": "SECURITY.md",
  "language_code": "tw"
}
-->
# 安全性

Microsoft 非常重視我們軟體產品與服務的安全性，這也包括透過我們的 GitHub 組織管理的所有原始碼庫，例如 [Microsoft](https://github.com/Microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet) 和 [Xamarin](https://github.com/xamarin)。

如果您認為在任何 Microsoft 擁有的原始碼庫中發現了符合 [Microsoft 安全性漏洞定義](https://aka.ms/security.md/definition) 的安全性漏洞，請按照以下描述向我們報告。

## 報告安全性問題

**請不要透過公開的 GitHub 問題來報告安全性漏洞。**

相反，請透過 Microsoft 安全性回應中心 (MSRC) 報告漏洞：[https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report)。

如果您偏好在不登入的情況下提交，請發送電子郵件至 [<EMAIL>](mailto:<EMAIL>)。如果可能，請使用我們的 PGP 金鑰加密您的訊息；您可以從 [Microsoft 安全性回應中心 PGP 金鑰頁面](https://aka.ms/security.md/msrc/pgp) 下載。

您應該會在 24 小時內收到回應。如果因某些原因未收到回應，請透過電子郵件進一步跟進，以確保我們收到您的原始訊息。更多資訊請參閱 [microsoft.com/msrc](https://www.microsoft.com/msrc)。

請盡可能提供以下所需資訊，幫助我們更好地理解問題的性質與範圍：

* 問題類型（例如，緩衝區溢位、SQL 注入、跨站腳本攻擊等）
* 與問題相關的原始檔案的完整路徑
* 受影響原始碼的位置（標籤/分支/提交或直接 URL）
* 重現問題所需的任何特殊配置
* 重現問題的逐步指導
* 概念驗證或攻擊代碼（如果可能）
* 問題的影響，包括攻擊者可能如何利用該問題

這些資訊將幫助我們更快速地處理您的報告。

如果您是為了漏洞賞金計畫進行報告，提供更完整的報告可能有助於獲得更高的賞金獎勵。請訪問我們的 [Microsoft 漏洞賞金計畫](https://aka.ms/security.md/msrc/bounty) 頁面，了解更多關於我們現行計畫的細節。

## 偏好語言

我們偏好所有的溝通以英文進行。

## 政策

Microsoft 遵循 [協調式漏洞披露](https://aka.ms/security.md/cvd) 原則。

**免責聲明**:  
本文件使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。儘管我們努力確保翻譯的準確性，但請注意，自動翻譯可能會包含錯誤或不精確之處。原始語言的文件應被視為具權威性的來源。對於關鍵信息，建議使用專業的人工翻譯。我們對因使用此翻譯而引起的任何誤解或誤讀不承擔責任。