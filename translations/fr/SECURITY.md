<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T10:03:42+00:00",
  "source_file": "SECURITY.md",
  "language_code": "fr"
}
-->
# Sécurité

Microsoft prend très au sérieux la sécurité de ses produits logiciels et services, y compris tous les dépôts de code source gérés via nos organisations GitHub, qui incluent [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) et [Xamarin](https://github.com/xamarin).

Si vous pensez avoir identifié une vulnérabilité de sécurité dans un dépôt appartenant à Microsoft et répondant à [la définition d'une vulnérabilité de sécurité de Microsoft](https://aka.ms/security.md/definition), veuillez nous en informer comme décrit ci-dessous.

## Signaler des problèmes de sécurité

**Veuillez ne pas signaler les vulnérabilités de sécurité via des problèmes publics sur GitHub.**

À la place, veuillez les signaler au Microsoft Security Response Center (MSRC) via [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report).

Si vous préférez soumettre sans vous connecter, envoyez un email à [<EMAIL>](mailto:<EMAIL>). Si possible, chiffrez votre message avec notre clé PGP ; vous pouvez la télécharger depuis la [page de la clé PGP du Microsoft Security Response Center](https://aka.ms/security.md/msrc/pgp).

Vous devriez recevoir une réponse sous 24 heures. Si, pour une raison quelconque, vous ne recevez pas de réponse, veuillez nous recontacter par email pour vérifier que nous avons bien reçu votre message initial. Des informations supplémentaires sont disponibles sur [microsoft.com/msrc](https://www.microsoft.com/msrc).

Veuillez inclure les informations demandées ci-dessous (dans la mesure du possible) pour nous aider à mieux comprendre la nature et l'ampleur du problème potentiel :

* Type de problème (par exemple, débordement de tampon, injection SQL, script intersite, etc.)
* Chemins complets des fichiers source liés à la manifestation du problème
* L'emplacement du code source affecté (tag/branche/commit ou URL directe)
* Toute configuration spéciale nécessaire pour reproduire le problème
* Instructions détaillées pour reproduire le problème
* Code de preuve de concept ou exploit (si possible)
* Impact du problème, y compris la manière dont un attaquant pourrait l'exploiter

Ces informations nous aideront à prioriser votre signalement plus rapidement.

Si vous signalez dans le cadre d'un programme de récompense pour bugs, des rapports plus complets peuvent contribuer à une récompense plus élevée. Veuillez consulter la page de notre [Programme de récompense pour bugs de Microsoft](https://aka.ms/security.md/msrc/bounty) pour plus de détails sur nos programmes actifs.

## Langues préférées

Nous préférons que toutes les communications soient en anglais.

## Politique

Microsoft suit le principe de [Divulgation coordonnée des vulnérabilités](https://aka.ms/security.md/cvd).

**Clause de non-responsabilité** :  
Ce document a été traduit à l'aide du service de traduction automatique [Co-op Translator](https://github.com/Azure/co-op-translator). Bien que nous nous efforcions de garantir l'exactitude, veuillez noter que les traductions automatisées peuvent contenir des erreurs ou des inexactitudes. Le document original dans sa langue d'origine doit être considéré comme la source faisant autorité. Pour des informations critiques, il est recommandé de recourir à une traduction humaine professionnelle. Nous ne sommes pas responsables des malentendus ou des interprétations erronées résultant de l'utilisation de cette traduction.