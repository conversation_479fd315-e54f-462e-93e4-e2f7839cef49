<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T09:08:26+00:00",
  "source_file": "SECURITY.md",
  "language_code": "pl"
}
-->
# Bezpieczeństwo

Microsoft traktuje bezpieczeństwo naszych produktów i usług oprogramowania bardzo poważnie, w tym wszystkich repozytoriów kodu źródłowego zarządzanych przez nasze organizacje na GitHubie, takie jak [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) i [Xamarin](https://github.com/xamarin).

<PERSON><PERSON><PERSON>, że znalazłeś lukę w zabezpieczeniach w którymkolwiek z repozytoriów należących do Microsoftu, która spełnia [definicję luki w zabezpieczeniach według Microsoftu](https://aka.ms/security.md/definition), zgłoś ją do nas zgodnie z poniższymi instrukcjami.

## Zgłaszanie problemów z bezpieczeństwem

**Prosimy, aby nie zgłaszać luk w zabezpieczeniach za pośrednictwem publicznych zgłoszeń na GitHubie.**

Zamiast tego zgłoś je do Microsoft Security Response Center (MSRC) na stronie [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report).

Jeśli wolisz zgłosić problem bez logowania się, wyślij e-mail na adres [<EMAIL>](mailto:<EMAIL>). Jeśli to możliwe, zaszyfruj swoją wiadomość naszym kluczem PGP; możesz go pobrać ze strony [Microsoft Security Response Center PGP Key](https://aka.ms/security.md/msrc/pgp).

Powinieneś otrzymać odpowiedź w ciągu 24 godzin. Jeśli z jakiegoś powodu jej nie otrzymasz, skontaktuj się ponownie za pomocą e-maila, aby upewnić się, że otrzymaliśmy Twoją pierwotną wiadomość. Dodatkowe informacje można znaleźć na stronie [microsoft.com/msrc](https://www.microsoft.com/msrc).

Prosimy o uwzględnienie poniższych informacji (w miarę możliwości), aby pomóc nam lepiej zrozumieć charakter i zakres potencjalnego problemu:

* Rodzaj problemu (np. przepełnienie bufora, SQL injection, cross-site scripting itp.)
* Pełne ścieżki plików źródłowych związanych z manifestacją problemu
* Lokalizacja dotkniętego kodu źródłowego (tag/gałąź/commit lub bezpośredni URL)
* Wszelkie specjalne konfiguracje wymagane do odtworzenia problemu
* Instrukcje krok po kroku, jak odtworzyć problem
* Kod proof-of-concept lub exploit (jeśli to możliwe)
* Wpływ problemu, w tym sposób, w jaki atakujący może wykorzystać lukę

Te informacje pomogą nam szybciej sklasyfikować Twoje zgłoszenie.

Jeśli zgłaszasz problem w ramach programu bug bounty, bardziej szczegółowe raporty mogą przyczynić się do przyznania wyższej nagrody. Odwiedź naszą stronę [Microsoft Bug Bounty Program](https://aka.ms/security.md/msrc/bounty), aby uzyskać więcej informacji o naszych aktywnych programach.

## Preferowane języki

Preferujemy, aby cała komunikacja odbywała się w języku angielskim.

## Polityka

Microsoft kieruje się zasadą [Koordynowanego ujawniania luk w zabezpieczeniach](https://aka.ms/security.md/cvd).

**Zastrzeżenie**:  
Ten dokument został przetłumaczony za pomocą usługi tłumaczeniowej AI [Co-op Translator](https://github.com/Azure/co-op-translator). Chociaż dokładamy wszelkich starań, aby tłumaczenie było precyzyjne, prosimy pamiętać, że automatyczne tłumaczenia mogą zawierać błędy lub nieścisłości. Oryginalny dokument w jego rodzimym języku powinien być uznawany za autorytatywne źródło. W przypadku informacji o krytycznym znaczeniu zaleca się skorzystanie z profesjonalnego tłumaczenia wykonanego przez człowieka. Nie ponosimy odpowiedzialności za wszelkie nieporozumienia lub błędne interpretacje wynikające z użycia tego tłumaczenia.