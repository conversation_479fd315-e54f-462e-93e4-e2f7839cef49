<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "50518c351b4501f2649aeaba31c2592e",
  "translation_date": "2025-03-28T10:04:42+00:00",
  "source_file": "SUPPORT.md",
  "language_code": "de"
}
-->
# TODO: Der Betreuer dieses Repos hat diese Datei noch nicht bearbeitet

**REPO-BETREUER**: Möchten Sie Kundendienst und Support (CSS) für dieses Produkt/Projekt?

- **Kein CSS-Support:** Füllen Sie diese Vorlage mit Informationen darüber aus, wie man Probleme meldet und Hilfe erhält.
- **Ja, CSS-Support:** Füllen Sie ein Intake-Formular unter [aka.ms/onboardsupport](https://aka.ms/onboardsupport) aus. CSS wird mit Ihnen zusammenarbeiten/helfen, die nächsten Schritte zu bestimmen.
- **Nicht sicher?** Füllen Sie ein Intake-Formular aus, als wäre die Antwort "Ja". CSS wird Ihnen bei der Entscheidung helfen.

*Entfernen Sie dann diese erste Überschrift aus dieser SUPPORT.MD-Datei, bevor Sie Ihr Repo veröffentlichen.*

## Support

## So melden Sie Probleme und erhalten Hilfe  

Dieses Projekt verwendet GitHub Issues, um Fehler und Funktionsanfragen zu verfolgen. Bitte durchsuchen Sie die bestehenden Issues, bevor Sie neue Issues erstellen, um Duplikate zu vermeiden. Für neue Issues erstellen Sie Ihren Fehler oder Ihre Funktionsanfrage als neues Issue.

Für Hilfe und Fragen zur Nutzung dieses Projekts wenden Sie sich bitte **REPO-BETREUER: FÜGEN SIE ANWEISUNGEN HIER EIN, WIE MAN SICH AN DIE REPO-BETREUER ODER DIE COMMUNITY WENDEN KANN, UM HILFE ZU ERHALTEN. DAS KÖNNTE EIN STACK OVERFLOW-TAG ODER EIN ANDERER KANAL SEIN. WO WERDEN SIE LEUTEN HELFEN?**.

## Microsoft-Support-Richtlinie  

Der Support für dieses **PROJEKT oder PRODUKT** beschränkt sich auf die oben aufgeführten Ressourcen.

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner Ausgangssprache sollte als maßgebliche Quelle betrachtet werden. Für kritische Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir haften nicht für Missverständnisse oder Fehlinterpretationen, die sich aus der Nutzung dieser Übersetzung ergeben.