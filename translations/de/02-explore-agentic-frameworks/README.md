<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "d3ceafa2939ede602b96d6bd412c5cbf",
  "translation_date": "2025-03-28T10:16:38+00:00",
  "source_file": "02-explore-agentic-frameworks\\README.md",
  "language_code": "de"
}
-->
[![Erkundung von KI-Agenten-Frameworks](../../../translated_images/lesson-2-thumbnail.807a3a4fc57057096d10678bf84638d17d50c50239014e75a7708731a33bb802.de.png)](https://youtu.be/ODwF-EZo_O8?si=1xoy_B9RNQfrYdF7)

> _(Klicken Sie auf das Bild oben, um das Video dieser Lektion anzusehen)_

# Erkundung von KI-Agenten-Frameworks

KI-Agenten-Frameworks sind Softwareplattformen, die die Erstellung, Bereitstellung und Verwaltung von KI-Agenten vereinfachen sollen. Diese Frameworks bieten Entwicklern vorgefertigte Komponenten, Abstraktionen und Tools, die die Entwicklung komplexer KI-Systeme erleichtern.

Diese Frameworks helfen Entwicklern, sich auf die einzigartigen Aspekte ihrer Anwendungen zu konzentrieren, indem sie standardisierte Ansätze für häufige Herausforderungen in der Entwicklung von KI-Agenten bereitstellen. Sie verbessern die Skalierbarkeit, Zugänglichkeit und Effizienz beim Aufbau von KI-Systemen.

## Einführung 

Diese Lektion behandelt:

- Was sind KI-Agenten-Frameworks und was ermöglichen sie Entwicklern?
- Wie können Teams diese nutzen, um schnell Prototypen zu erstellen, zu iterieren und die Fähigkeiten ihrer Agenten zu verbessern?
- Was sind die Unterschiede zwischen den Frameworks und Tools, die von Microsoft,
Modularität, Zusammenarbeit, Prozess-Orchestrierung | Sichere, skalierbare und flexible Bereitstellung von KI-Agenten | Was ist der ideale Anwendungsfall für jedes dieser Frameworks?

## Kann ich meine bestehenden Azure-Ökosystem-Tools direkt integrieren, oder benötige ich eigenständige Lösungen?

Die Antwort ist ja, Sie können Ihre bestehenden Azure-Ökosystem-Tools direkt mit Azure AI Agent Service integrieren, insbesondere da dieser Dienst so konzipiert wurde, dass er nahtlos mit anderen Azure-Diensten zusammenarbeitet. Sie könnten beispielsweise Bing, Azure AI Search und Azure Functions integrieren. Es gibt auch eine tiefe Integration mit Azure AI Foundry. 

Für AutoGen und Semantic Kernel können Sie ebenfalls mit Azure-Diensten integrieren, allerdings könnte es erforderlich sein, die Azure-Dienste aus Ihrem Code heraus aufzurufen. Eine weitere Möglichkeit zur Integration besteht darin, die Azure SDKs zu nutzen, um von Ihren Agenten aus mit Azure-Diensten zu interagieren. Zusätzlich können Sie, wie bereits erwähnt, Azure AI Agent Service als Orchestrator für Ihre in AutoGen oder Semantic Kernel erstellten Agenten verwenden, was Ihnen einfachen Zugang zum Azure-Ökosystem verschafft.

## Referenzen

## Vorherige Lektion

[Einführung in KI-Agenten und deren Anwendungsfälle](../01-intro-to-ai-agents/README.md)

## Nächste Lektion

[Verständnis von agentischen Designmustern](../03-agentic-design-patterns/README.md)

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner ursprünglichen Sprache sollte als maßgebliche Quelle betrachtet werden. Für kritische Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir übernehmen keine Haftung für Missverständnisse oder Fehlinterpretationen, die durch die Nutzung dieser Übersetzung entstehen.