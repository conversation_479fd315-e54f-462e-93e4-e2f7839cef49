<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T10:03:21+00:00",
  "source_file": "SECURITY.md",
  "language_code": "de"
}
-->
# Sicherheit

Microsoft nimmt die Sicherheit seiner Softwareprodukte und -dienste sehr ernst, einschließlich aller Quellcode-Repositories, die über unsere GitHub-Organisationen verwaltet werden, zu denen [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) und [Xamarin](https://github.com/xamarin) gehören.

Falls Sie glauben, eine Sicherheitslücke in einem von Microsoft verwalteten Repository gefunden zu haben, die [Microsofts Definition einer Sicherheitslücke](https://aka.ms/security.md/definition) entspricht, melden Sie diese bitte wie unten beschrieben.

## Melden von Sicherheitsproblemen

**Bitte melden Sie Sicherheitslücken nicht über öffentliche GitHub-Issues.**

Stattdessen melden Sie diese bitte dem Microsoft Security Response Center (MSRC) unter [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report).

Falls Sie es bevorzugen, ohne Anmeldung einzureichen, senden Sie eine E-Mail an [<EMAIL>](mailto:<EMAIL>). Wenn möglich, verschlüsseln Sie Ihre Nachricht mit unserem PGP-Schlüssel; Sie können ihn auf der [Microsoft Security Response Center PGP Key Seite](https://aka.ms/security.md/msrc/pgp) herunterladen.

Sie sollten innerhalb von 24 Stunden eine Antwort erhalten. Falls dies aus irgendeinem Grund nicht geschieht, folgen Sie bitte per E-Mail nach, um sicherzustellen, dass wir Ihre ursprüngliche Nachricht erhalten haben. Weitere Informationen finden Sie unter [microsoft.com/msrc](https://www.microsoft.com/msrc).

Bitte geben Sie so viele der unten angeforderten Informationen wie möglich an, um uns zu helfen, die Natur und den Umfang des möglichen Problems besser zu verstehen:

* Art des Problems (z. B. Buffer Overflow, SQL Injection, Cross-Site-Scripting usw.)
* Vollständige Pfade der Quellcodedatei(en), die mit dem Problem zusammenhängen
* Der Standort des betroffenen Quellcodes (Tag/Branch/Commit oder direkte URL)
* Jegliche spezielle Konfiguration, die erforderlich ist, um das Problem zu reproduzieren
* Schritt-für-Schritt-Anleitung zur Reproduktion des Problems
* Proof-of-Concept oder Exploit-Code (falls möglich)
* Auswirkungen des Problems, einschließlich wie ein Angreifer das Problem ausnutzen könnte

Diese Informationen helfen uns, Ihre Meldung schneller zu priorisieren.

Falls Sie im Rahmen eines Bug-Bounty-Programms melden, können umfassendere Berichte zu einer höheren Prämie führen. Bitte besuchen Sie unsere [Microsoft Bug Bounty Program](https://aka.ms/security.md/msrc/bounty) Seite, um weitere Details über unsere aktiven Programme zu erfahren.

## Bevorzugte Sprachen

Wir bevorzugen alle Kommunikation in Englisch.

## Richtlinie

Microsoft folgt dem Prinzip der [Koordinierten Offenlegung von Sicherheitslücken](https://aka.ms/security.md/cvd).

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner ursprünglichen Sprache sollte als maßgebliche Quelle betrachtet werden. Für kritische Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir übernehmen keine Haftung für Missverständnisse oder Fehlinterpretationen, die durch die Nutzung dieser Übersetzung entstehen.