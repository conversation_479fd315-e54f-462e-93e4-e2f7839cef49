<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T10:02:37+00:00",
  "source_file": "SECURITY.md",
  "language_code": "es"
}
-->
# Seguridad

Microsoft se toma muy en serio la seguridad de nuestros productos y servicios de software, lo que incluye todos los repositorios de código fuente gestionados a través de nuestras organizaciones de GitHub, que incluyen [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) y [Xamarin](https://github.com/xamarin).

Si crees que has encontrado una vulnerabilidad de seguridad en algún repositorio propiedad de Microsoft que cumpla con [la definición de vulnerabilidad de seguridad de Microsoft](https://aka.ms/security.md/definition), infórmanos como se describe a continuación.

## Informar sobre problemas de seguridad

**Por favor, no informes sobre vulnerabilidades de seguridad a través de problemas públicos en GitHub.**

En su lugar, infórmalas al Centro de Respuesta de Seguridad de Microsoft (MSRC) en [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report).

Si prefieres enviar tu informe sin iniciar sesión, envía un correo electrónico a [<EMAIL>](mailto:<EMAIL>). Si es posible, cifra tu mensaje con nuestra clave PGP; puedes descargarla desde la [página de la clave PGP del Centro de Respuesta de Seguridad de Microsoft](https://aka.ms/security.md/msrc/pgp).

Deberías recibir una respuesta en un plazo de 24 horas. Si por alguna razón no la recibes, realiza un seguimiento por correo electrónico para asegurarte de que recibimos tu mensaje original. Puedes encontrar información adicional en [microsoft.com/msrc](https://www.microsoft.com/msrc).

Por favor, incluye la información solicitada a continuación (tanto como puedas proporcionar) para ayudarnos a comprender mejor la naturaleza y el alcance del posible problema:

* Tipo de problema (por ejemplo, desbordamiento de búfer, inyección SQL, scripting entre sitios, etc.)
* Rutas completas de los archivos fuente relacionados con la manifestación del problema
* La ubicación del código fuente afectado (etiqueta/ramo/commit o URL directa)
* Cualquier configuración especial necesaria para reproducir el problema
* Instrucciones paso a paso para reproducir el problema
* Código de prueba de concepto o de explotación (si es posible)
* Impacto del problema, incluyendo cómo un atacante podría explotarlo

Esta información nos ayudará a priorizar tu informe más rápidamente.

Si estás informando para un programa de recompensas por errores, los informes más completos pueden contribuir a una mayor recompensa. Por favor, visita nuestra página del [Programa de Recompensas por Errores de Microsoft](https://aka.ms/security.md/msrc/bounty) para más detalles sobre nuestros programas activos.

## Idiomas preferidos

Preferimos que todas las comunicaciones sean en inglés.

## Política

Microsoft sigue el principio de [Divulgación Coordinada de Vulnerabilidades](https://aka.ms/security.md/cvd).

**Descargo de responsabilidad**:  
Este documento ha sido traducido utilizando el servicio de traducción automática [Co-op Translator](https://github.com/Azure/co-op-translator). Aunque nos esforzamos por garantizar la precisión, tenga en cuenta que las traducciones automáticas pueden contener errores o imprecisiones. El documento original en su idioma nativo debe considerarse la fuente autorizada. Para información crítica, se recomienda una traducción profesional realizada por humanos. No nos hacemos responsables de malentendidos o interpretaciones erróneas que puedan surgir del uso de esta traducción.