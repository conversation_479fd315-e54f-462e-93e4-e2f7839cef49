<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "50518c351b4501f2649aeaba31c2592e",
  "translation_date": "2025-03-28T10:04:23+00:00",
  "source_file": "SUPPORT.md",
  "language_code": "es"
}
-->
# TODO: El responsable de este repositorio aún no ha editado este archivo

**PROPIETARIO DEL REPOSITORIO**: ¿Deseas soporte de Customer Service & Support (CSS) para este producto/proyecto?

- **Sin soporte CSS:** Completa esta plantilla con información sobre cómo reportar problemas y obtener ayuda.
- **Con soporte CSS:** Llena un formulario de ingreso en [aka.ms/onboardsupport](https://aka.ms/onboardsupport). CSS trabajará contigo para determinar los próximos pasos.
- **¿No estás seguro?** Llena el formulario de ingreso como si la respuesta fuera "Sí". CSS te ayudará a decidir.

*Luego elimina este primer encabezado de este archivo SUPPORT.MD antes de publicar tu repositorio.*

## Soporte

## Cómo reportar problemas y obtener ayuda  

Este proyecto utiliza GitHub Issues para rastrear errores y solicitudes de características. Por favor, busca entre los problemas existentes antes de crear nuevos para evitar duplicados. Para nuevos problemas, reporta tu error o solicitud de característica como un nuevo Issue.

Para obtener ayuda y realizar preguntas sobre el uso de este proyecto, por favor **RESPONSABLE DEL REPOSITORIO: INSERTA AQUÍ LAS INSTRUCCIONES SOBRE CÓMO CONTACTAR A LOS RESPONSABLES DEL REPOSITORIO O A LA COMUNIDAD PARA OBTENER AYUDA. PODRÍA SER UNA ETIQUETA DE STACK OVERFLOW U OTRO CANAL. ¿DÓNDE AYUDARÁS A LAS PERSONAS?**.

## Política de soporte de Microsoft  

El soporte para este **PROYECTO o PRODUCTO** se limita a los recursos mencionados anteriormente.

**Descargo de responsabilidad**:  
Este documento ha sido traducido utilizando el servicio de traducción automática [Co-op Translator](https://github.com/Azure/co-op-translator). Aunque nos esforzamos por garantizar la precisión, tenga en cuenta que las traducciones automatizadas pueden contener errores o imprecisiones. El documento original en su idioma nativo debe considerarse la fuente autorizada. Para información crítica, se recomienda una traducción profesional realizada por humanos. No nos hacemos responsables de malentendidos o interpretaciones erróneas que puedan surgir del uso de esta traducción.