# AI代理的生产部署

## 简介

本课程将涵盖以下内容：

- 如何有效规划将AI代理部署到生产环境中。
- 部署AI代理到生产环境时可能遇到的常见错误和问题。
- 如何在保持AI代理性能的同时管理成本。

## 学习目标

完成本课程后，您将了解如何/能够：

- 提高生产环境中AI代理系统的性能、成本效率和效果的技术。
- 如何评估您的AI代理及其具体方法。
- 控制AI代理生产部署成本的策略。

部署值得信赖的AI代理非常重要。请参阅“构建值得信赖的AI代理”课程以了解更多内容。

## 评估AI代理

在部署AI代理之前、期间和之后，建立一个完善的评估系统至关重要。这可以确保您的系统与您和用户的目标保持一致。

评估AI代理时，您需要能够评估的不仅仅是代理的输出，还包括AI代理所运行的整个系统。这包括但不限于：

- 初始模型请求。
- 代理识别用户意图的能力。
- 代理识别执行任务所需正确工具的能力。
- 工具对代理请求的响应。
- 代理解释工具响应的能力。
- 用户对代理响应的反馈。

这种方法可以帮助您以更模块化的方式识别改进领域。然后，您可以更高效地监控模型、提示词、工具以及其他组件更改的效果。

## AI代理的常见问题及潜在解决方案

| **问题**                                     | **潜在解决方案**                                                                                                                                                                                                          |
| -------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| AI代理任务执行不一致                        | - 优化提供给AI代理的提示词，明确目标。<br>- 确定是否可以将任务分解为子任务，并通过多个代理分别处理。                                                                                  |
| AI代理陷入连续循环                          | - 确保定义清晰的终止条件，让代理知道何时停止流程。<br>- 对于需要推理和规划的复杂任务，使用专为推理任务设计的大型模型。                                                                  |
| AI代理调用工具表现不佳                      | - 在代理系统外测试并验证工具的输出。<br>- 优化工具的参数设置、提示词和命名方式。                                                                                                    |
| 多代理系统表现不一致                        | - 优化提供给每个代理的提示词，确保它们具体且相互区分。<br>- 构建一个使用“路由”或控制器代理的分层系统，以确定哪个代理是正确的选择。                                                    |

## 成本管理

以下是一些在生产环境中部署AI代理的成本管理策略：

- **缓存响应** - 识别常见请求和任务，并在它们通过您的代理系统之前提供响应，这是减少类似请求量的好方法。您甚至可以通过更基础的AI模型实现一个流程，来判断某个请求与缓存请求的相似度。

- **使用小型模型** - 小型语言模型（SLM）在某些代理使用场景中表现良好，并能显著降低成本。如前所述，建立一个评估系统来确定并比较其与大型模型的性能是了解SLM在您的使用场景中表现的最佳方式。

- **使用路由模型** - 类似的策略是使用不同规模的模型组合。您可以使用LLM/SLM或无服务器函数，根据请求的复杂性将其路由到最合适的模型。这不仅能降低成本，还能确保复杂任务的性能表现。

## 恭喜

这是“AI代理入门”课程的最后一课。

我们计划根据反馈和这个快速发展的行业变化不断增加新课程，因此欢迎您不久后再回来查看。

如果您想继续学习和构建AI代理，欢迎加入 [Azure AI Community Discord](https://discord.gg/kzRShWzttr)。

我们在这里举办工作坊、社区圆桌会议以及“问我任何问题”环节。

此外，我们还有一个学习资料集合，其中包含更多帮助您开始在生产环境中构建AI代理的资源。

**免责声明**：  
本文件使用基于机器的人工智能翻译服务进行翻译。尽管我们努力确保翻译的准确性，但请注意，自动翻译可能包含错误或不准确之处。应以原始语言的文件作为权威来源。对于关键信息，建议使用专业的人类翻译服务。对于因使用本翻译而产生的任何误解或误读，我们概不负责。