# 初学者的 AI 代理课程

![生成式 AI 入门](../../translated_images/repo-thumbnail.fdd5f487bb7274d4a08459d76907ec4914de268c99637e9af082b1d3eb0730e2.zh.png?WT.mc_id=academic-105485-koreyst)

## 10 节课程，教您从零开始构建 AI 代理的一切知识

[![GitHub license](https://img.shields.io/github/license/microsoft/ai-agents-for-beginners.svg)](https://github.com/microsoft/ai-agents-for-beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/ai-agents-for-beginners.svg)](https://GitHub.com/microsoft/ai-agents-for-beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/ai-agents-for-beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/ai-agents-for-beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/ai-agents-for-beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/ai-agents-for-beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/ai-agents-for-beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/ai-agents-for-beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![Azure AI Discord](https://dcbadge.limes.pink/api/server/kzRShWzttr)](https://discord.gg/kzRShWzttr)

## 🌐 多语言支持

| 语言                 | 代码 | 翻译后的 README 链接                                   | 最后更新日期       |
|----------------------|------|-----------------------------------------------------|--------------------|
| 简体中文             | zh   | [中文翻译](./README.md)              | 2025-03-28         |
| 繁体中文             | tw   | [中文翻译](../tw/README.md)              | 2025-03-28         |
| 法语                 | fr   | [法语翻译](../fr/README.md)              | 2025-03-28         |
| 日语                 | ja   | [日语翻译](../ja/README.md)              | 2025-03-28         |
| 韩语                 | ko   | [韩语翻译](../ko/README.md)              | 2025-03-28         |
| 西班牙语             | es   | [西班牙语翻译](../es/README.md)          | 2025-03-28         |

> **注意:**
> 这些翻译是使用开源工具 [co-op-translator](https://github.com/Azure/co-op-translator) 自动生成的，可能存在错误或不准确之处。对于重要信息，请参考原文或寻求专业人工翻译。如果您想贡献或更新翻译，请访问该仓库，您可以通过简单的命令轻松进行贡献。

## 🌱 快速开始

本课程包含 10 节课，涵盖构建 AI 代理的基础知识。每节课都专注于一个独立主题，因此您可以从任何感兴趣的地方开始！

如果这是您第一次使用生成式 AI 模型进行构建，请查看我们的 [生成式 AI 入门课程](https://aka.ms/genai-beginners)，该课程包含 21 节课，讲解如何使用生成式 AI。

### 如何运行本课程的代码

本课程在每节课中提供了包含 `code_samples` 文件夹的代码示例。这些代码使用以下模型服务：

- [Github Models](https://aka.ms/ai-agents-beginners/github-models) - 免费 / 有限
- [Azure AI Foundry](https://aka.ms/ai-agents-beginners/ai-foundry) - 需要 Azure 账号

此外，本课程还使用以下 AI 代理框架和服务：

- [Azure AI Agent Service](https://aka.ms/ai-agents-beginners/ai-agent-service)
- [Semantic Kernel](https://aka.ms/ai-agents-beginners/semantic-kernel)
- [AutoGen](https://aka.ms/ai-agents/autogen)

有关运行本课程代码的更多信息，请参阅 [课程设置](./00-course-setup/README.md)。

别忘了 [给这个仓库点星 (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) 和 [fork 这个仓库](https://github.com/microsoft/ai-agents-for-beginners/fork) 来运行代码。

## 🙏 想要贡献？

您有建议或发现拼写或代码错误吗？请[提交问题](https://github.com/microsoft/ai-agents-for-beginners/issues?WT.mc_id=academic-105485-koreyst)或[创建拉取请求](https://github.com/microsoft/ai-agents-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)。

如果您遇到困难或对构建 AI 代理有任何疑问，请加入我们的 [Azure AI 社区 Discord](https://discord.gg/kzRShWzttr)。

## 📂 每节课包括

- 写在 README 中的课程内容（视频将于 2025 年 3 月上线）
- 支持 Azure AI Foundry 和 Github Models（免费）的 Python 代码示例
- 指向额外学习资源的链接，帮助您继续学习

## 🗃️ 课程目录

| **课程**                             | **链接**                                   | **额外学习资源**   |
|-------------------------------------|------------------------------------------|--------------------|
| AI 代理简介及应用场景               | [AI 代理简介及应用场景](./01-intro-to-ai-agents/README.md) | 了解更多           |
| 探索 Agentic 框架                | [Exploring Agentic Frameworks](./02-explore-agentic-frameworks/README.md)  | 了解更多         |
| 理解 Agentic 设计模式            | [Understanding Agentic Design Patterns](./03-agentic-design-patterns/README.md)  | 了解更多         |
| 工具使用设计模式                 | [Tool Use Design Pattern](./04-tool-use/README.md)                    | 了解更多         |
| Agentic RAG                      | [Agentic RAG](./05-agentic-rag/README.md)                 | 了解更多         |
| 构建可信赖的 AI 代理             | [Building Trustworthy AI Agents](./06-building-trustworthy-agents/README.md) | 了解更多         |
| 规划设计模式                     | [Planning Design Pattern](./07-planning-design/README.md)             | 了解更多         |
| 多代理设计模式                   | [Muilt-Agent Design Pattern](./08-multi-agent/README.md)                 | 了解更多         |
| 元认知设计模式                   | [Metacognition Design Pattern](./09-metacognition/README.md)               | 了解更多         |
| 生产环境中的 AI 代理             | [AI Agents in Production](./10-ai-agents-production/README.md)        | 了解更多         |

## 🎒 其他课程

我们的团队还制作了其他课程！欢迎查看：

- [Generative AI for Beginners](https://aka.ms/genai-beginners)
- [ML for Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science for Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI for Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)

## 贡献指南

本项目欢迎贡献和建议。大多数贡献需要您同意一份贡献者许可协议 (CLA)，声明您有权并实际授予我们使用您贡献的权利。详情请访问 <https://cla.opensource.microsoft.com>。

当您提交一个 pull request 时，CLA 机器人会自动判断您是否需要提供 CLA，并相应地标记 PR（例如状态检查、评论）。只需按照机器人提供的指示操作即可。您只需在所有使用我们 CLA 的仓库中执行一次此操作。

本项目采用了 [Microsoft 开源行为准则](https://opensource.microsoft.com/codeofconduct/)。  
如需更多信息，请参阅 [行为准则 FAQ](https://opensource.microsoft.com/codeofconduct/faq/) 或通过 [<EMAIL>](mailto:<EMAIL>) 联系我们提出其他问题或评论。

## 商标声明

本项目可能包含项目、产品或服务的商标或徽标。使用 Microsoft 商标或徽标需获得授权，并必须遵循 [Microsoft 的商标和品牌指南](https://www.microsoft.com/legal/intellectualproperty/trademarks/usage/general)。  
在修改版本的项目中使用 Microsoft 商标或徽标不得引起混淆或暗示 Microsoft 的赞助。  
任何对第三方商标或徽标的使用均需遵守该第三方的政策。

**免责声明**：  
本文档通过基于机器的AI翻译服务翻译而成。尽管我们努力确保准确性，但请注意，自动翻译可能包含错误或不准确之处。应以原始语言的文档作为权威来源。对于关键信息，建议寻求专业人工翻译。因使用本翻译而引起的任何误解或误读，我们概不负责。