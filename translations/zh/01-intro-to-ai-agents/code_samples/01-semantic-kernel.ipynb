{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>\n", "\n", "在此代码示例中，您将使用 [Semantic Kernel](https://aka.ms/ai-agents-beginners/semantic-kernel) AI 框架创建一个基本 Agent。\n", "\n", "此示例的目的是向您展示我们稍后在实现不同 Agentic 模式的其他代码示例中将使用的步骤。\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 导入所需的 Python 依赖包 "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os \n", "from typing import Annotated\n", "from openai import AsyncOpenAI\n", "\n", "from dotenv import load_dotenv\n", "\n", "\n", "\n", "from semantic_kernel.kernel import Kernel\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.agents import ChatCompletionAgent\n", "from semantic_kernel.contents import ChatHistory\n", "\n", "\n", "from semantic_kernel.agents.open_ai import OpenAIAssistantAgent\n", "from semantic_kernel.contents import AuthorRole, ChatMessageContent\n", "from semantic_kernel.functions import kernel_function\n", "\n", "from semantic_kernel.connectors.ai import FunctionChoiceBehavior\n", "\n", "from semantic_kernel.contents.function_call_content import FunctionCallContent\n", "from semantic_kernel.contents.function_result_content import FunctionResultContent\n", "from semantic_kernel.functions import KernelArguments, kernel_function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 创建客户端 (Client) 和内核 (Kernel)\n", "\n", "在此示例中，我们将使用 [GitHub Models](https://aka.ms/ai-agents-beginners/github-models) 来访问 LLM。\n", "\n", "`ai_model_id` 被定义为 `gpt-4o-mini`。尝试将模型更改为 GitHub Models 市场上可用的其他模型，以查看不同的结果。\n", "\n", "为了使用 `Azure Inference SDK`（用于 GitHub Models 的 `base_url`），我们将在 Semantic Kernel 中使用 `AsyncOpenAI` 连接器。 还有其他[可用连接器](https://learn.microsoft.com/semantic-kernel/concepts/ai-services/chat-completion)可将 Semantic Kernel 与其他模型提供商连接。\n", "\n", "我们还将创建一个内核 `Kernel`。`kernel` 是您的 Agents 将使用的服务和插件的集合。在此代码段中，我们正在创建内核并将 `chat_completion_service` 添加到其中。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from typing import Annotated\n", "from semantic_kernel import kernel_function\n", "\n", "# 为示例定义一个示例插件\n", "\n", "class DestinationsPlugin:\n", "    \"\"\"A List of Random Destinations for a vacation.\"\"\"\n", "\n", "    def __init__(self):\n", "        # 度假目的地列表\n", "        self.destinations = [\n", "            \"Barcelona, Spain\",\n", "            \"Paris, France\",\n", "            \"Berlin, Germany\",\n", "            \"Tokyo, Japan\",\n", "            \"Sydney, Australia\",\n", "            \"New York, USA\",\n", "            \"Cairo, Egypt\",\n", "            \"Cape Town, South Africa\",\n", "            \"Rio de Janeiro, Brazil\",\n", "            \"Bali, Indonesia\"\n", "        ]\n", "        # 跟踪上一个目的地以避免重复\n", "        self.last_destination = None\n", "\n", "    @kernel_function(description=\"Provides a random vacation destination.\")\n", "    def get_random_destination(self) -> Annotated[str, \"Returns a random vacation destination.\"]:\n", "        # 获取可用的目的地（如果可能，排除上一个）\n", "        available_destinations = self.destinations.copy()\n", "        if self.last_destination and len(available_destinations) > 1:\n", "            available_destinations.remove(self.last_destination)\n", "\n", "        # 选择一个随机的目的地\n", "        destination = random.choice(available_destinations)\n", "\n", "        # 更新上一个目的地\n", "        self.last_destination = destination\n", "\n", "        return destination\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "client = AsyncOpenAI(\n", "    api_key=os.environ.get(\"GITHUB_TOKEN\"), base_url=\"https://models.inference.ai.azure.com/\")\n", "\n", "kernel = Kernel()\n", "kernel.add_plugin(DestinationsPlugin(), plugin_name=\"destinations\")\n", "\n", "service_id = \"agent\"\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", "    service_id=service_id\n", ")\n", "kernel.add_service(chat_completion_service)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 创建 Agent\n", "\n", "下面我们将创建名为 `TravelAgent` 的 Agent，并创建一个名为 `AGENT_INSTRUCTIONS` 的变量。稍后我们会将其添加到我们的 `system_message` 中，该消息将向 Agent 提供有关任务、行为和语气的指示。\n", "\n", "对于此示例，我们使用非常简单的指令。您可以更改这些指令以查看 Agent 如何做出不同的响应。\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["settings = kernel.get_prompt_execution_settings_from_service_id(\n", "    service_id=service_id)\n", "settings.function_choice_behavior = FunctionChoiceBehavior.Auto()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["AGENT_NAME = \"TravelAgent\"\n", "AGENT_INSTRUCTIONS = \"You are a helpful AI Agent that can help plan vacations for customers at random destinations\"\n", "agent = ChatCompletionAgent(\n", "    service_id=service_id, \n", "    kernel=kernel, \n", "    name=AGENT_NAME,\n", "    instructions=AGENT_INSTRUCTIONS,\n", "    arguments=KernelArguments(settings=settings)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 运行 Agents\n", "\n", "现在我们可以通过定义 `ChatHistory` 并将 `system_message` 添加到其中来运行 Agent。我们将使用之前定义的 `AGENT_INSTRUCTIONS`。\n", "\n", "定义好这些之后，我们创建一个 `user_inputs`，它将是用户发送给 Agent 的内容。在这种情况下，我们将此消息设置为 `Plan me a sunny vacation`。\n", "\n", "您可以随意更改此消息，以查看 Agent 如何做出不同的响应。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div style='margin-bottom:10px'><div style='font-weight:bold'>User:</div><div style='margin-left:20px'>Plan me a day trip.</div></div><div style='margin-bottom:10px'><details><summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary><div style='margin:10px; padding:10px; background-color:#f8f8f8; border:1px solid #ddd; border-radius:4px; white-space:pre-wrap;'>Calling: get_random_destination()<br>Calling: ({})<br>Result: Rio de Janeiro, Brazil</div></details></div><div style='margin-bottom:20px'><div style='font-weight:bold'>TravelAgent:</div><div style='margin-left:20px; white-space:pre-wrap'>I've planned a day trip to the beautiful city of Rio de Janeiro, Brazil! Here’s a suggested itinerary for your day:\n", "\n", "### Morning- **Breakfast**: Start your day with a traditional Brazilian breakfast of pão de queijo (cheese bread) and fresh tropical fruit at a local café.\n", "- **Visit <PERSON> the Redeemer**: Head to the iconic Christ the Redeemer statue. You can take a train through the Tijuca Forest to reach the summit, and enjoy stunning views of the city along the way.\n", "\n", "### Midday- **Lunch**: Enjoy a leisurely lunch at a local restaurant in the Santa Teresa neighborhood. Try some feijoada, a hearty Brazilian stew.\n", "- **Explore Santa Teresa**: Stroll through the charming streets of Santa Teresa, filled with art galleries, colorful houses, and beautiful views.\n", "\n", "### Afternoon- **Sugarloaf Mountain**: Take the cable car to the top of Sugarloaf Mountain for panoramic views of Rio, including Copacabana and the sprawling coastline.\n", "- **Relax at Copacabana Beach**: Spend some time relaxing on the famous Copacabana Beach. If you're up for it, you can enjoy a refreshing swim or try out beach volleyball with locals.\n", "\n", "### Evening- **Dinner**: Dine at a seafood restaurant with a view of the beach, enjoying fresh catch of the day and a caipirinha cocktail, a Brazilian favorite.\n", "- **Nightlife**: If you’re up for it, explore the lively nightlife of Rio in Lapa, where you can find samba clubs and live music.\n", "\n", "Enjoy your day trip to Rio de Janeiro!</div></div><hr>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div style='margin-bottom:10px'><div style='font-weight:bold'>User:</div><div style='margin-left:20px'>I don't like that destination. Plan me another vacation.</div></div><div style='margin-bottom:10px'><details><summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary><div style='margin:10px; padding:10px; background-color:#f8f8f8; border:1px solid #ddd; border-radius:4px; white-space:pre-wrap;'>Calling: get_random_destination()<br>Calling: ({})<br>Result: Cape Town, South Africa</div></details></div><div style='margin-bottom:20px'><div style='font-weight:bold'>TravelAgent:</div><div style='margin-left:20px; white-space:pre-wrap'>How about a day trip to stunning Cape Town, South Africa? Here's a suggested itinerary:\n", "\n", "### Morning- **Breakfast**: Start your day with a hearty breakfast at a café along the V&A Waterfront, enjoying views of the harbor and Table Mountain.\n", "- **Table Mountain**: Take the cable car up to Table Mountain for breathtaking panoramic views of the city and the ocean. Don’t forget your camera!\n", "\n", "### Midday- **Lunch**: Enjoy lunch at one of the waterfront restaurants, where you can savor fresh seafood or local South African dishes.\n", "- **Visit Kirstenbosch National Botanical Garden**: Explore these beautiful gardens nestled at the foot of Table Mountain. Walk along the treetop canopy walkway for a unique perspective of the flora.\n", "\n", "### Afternoon- **Explore the Cape Peninsula**: Take a scenic drive along Chapman's Peak Drive to Cape Point. Here, you can hike to the lighthouse and enjoy spectacular ocean views.\n", "- **Boulders Beach**: Afterward, head to Boulders Beach to see the famous African penguin colony.\n", "\n", "### Evening- **Dinner**: Return to Cape Town for dinner at a restaurant in the city center or along the waterfront, where you can enjoy a fine dining experience featuring local wines and cuisine.\n", "- **Sunset at Signal Hill**: End your day by watching the sunset from Signal Hill, offering a stunning view of the city and the ocean.\n", "\n", "Enjoy your day trip in Cape Town!</div></div><hr>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, HTML\n", "\n", "\n", "async def main():\n", "    # 定义聊天历史\n", "    chat_history = ChatHistory()\n", "\n", "    # 响应用户输入\n", "    user_inputs = [\n", "        \"Plan me a day trip.\",  # 计划一个一日游。\n", "        \"I don't like that destination. Plan me another vacation.\",  # 我不喜欢那个目的地。为我计划另一个假期。\n", "    ]\n", "\n", "    for user_input in user_inputs:\n", "        # 将用户输入添加到聊天历史\n", "        chat_history.add_user_message(user_input)\n", "\n", "        # 开始构建 HTML 输出\n", "        html_output = f\"<div style='margin-bottom:10px'>\"\n", "        html_output += f\"<div style='font-weight:bold'>User:</div>\"  # 用户：\n", "        html_output += f\"<div style='margin-left:20px'>{user_input}</div>\"\n", "        html_output += f\"</div>\"\n", "\n", "        agent_name: str | None = None\n", "        full_response = \"\"\n", "        function_calls = []\n", "        function_results = {}\n", "\n", "        # 收集 Agent 的响应并跟踪函数调用\n", "        async for content in agent.invoke_stream(chat_history):\n", "            if not agent_name and hasattr(content, 'name'):\n", "                agent_name = content.name\n", "\n", "            # 跟踪函数调用和结果\n", "            for item in content.items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    call_info = f\"Calling: {item.function_name}({item.arguments})\"  # 正在调用：\n", "                    function_calls.append(call_info)\n", "                elif isinstance(item, FunctionResultContent):\n", "                    result_info = f\"Result: {item.result}\"  # 结果：\n", "                    function_calls.append(result_info)\n", "                    # 存储函数结果\n", "                    function_results[item.function_name] = item.result\n", "\n", "            # 如果内容不是与函数相关的消息，则将其添加到响应中\n", "            if (hasattr(content, 'content') and content.content and content.content.strip() and\n", "                not any(isinstance(item, (FunctionCallContent, FunctionResultContent))\n", "                        for item in content.items)):\n", "                full_response += content.content\n", "\n", "        # 如果发生任何函数调用，则将函数调用添加到 HTML\n", "        if function_calls:\n", "            html_output += f\"<div style='margin-bottom:10px'>\"\n", "            html_output += f\"<details>\"\n", "            html_output += f\"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"  # 函数调用（点击展开）\n", "            html_output += f\"<div style='margin:10px; padding:10px; background-color:#f8f8f8; border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"  # Increased font size and adjusted text color\n", "            html_output += \"<br>\".join(function_calls)\n", "            html_output += f\"</div></details></div>\"\n", "\n", "        # 将 Agent 响应添加到 HTML\n", "        html_output += f\"<div style='margin-bottom:20px'>\"\n", "        html_output += f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"  # 助手：\n", "        html_output += f\"<div style='margin-left:20px; white-space:pre-wrap'>{full_response}</div>\"\n", "        html_output += f\"</div>\"\n", "        html_output += \"<hr>\"\n", "\n", "        # 显示格式化的 HTML\n", "        display(HTML(html_output))\n", "\n", "await main()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}