# 安全性

Microsoft 非常重视我们软件产品和服务的安全性，这包括通过我们的 GitHub 组织管理的所有源代码库，这些组织包括 [Microsoft](https://github.com/Microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet) 和 [Xamarin](https://github.com/xamarin)。

如果您认为在任何 Microsoft 拥有的代码库中发现了安全漏洞，并且该漏洞符合 [Microsoft 对安全漏洞的定义](https://aka.ms/security.md/definition)，请按照以下说明向我们报告。

## 报告安全问题

**请不要通过公开的 GitHub 问题报告安全漏洞。**

请通过 Microsoft 安全响应中心 (MSRC) 报告安全问题，访问 [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report)。

如果您希望在不登录的情况下提交报告，可以发送电子邮件至 [<EMAIL>](mailto:<EMAIL>)。 如果可能，请使用我们的 PGP 密钥加密您的消息；您可以从 [Microsoft 安全响应中心 PGP 密钥页面](https://aka.ms/security.md/msrc/pgp) 下载。

您应该会在 24 小时内收到回复。如果由于某种原因未收到，请通过电子邮件进行跟进，以确保我们收到了您的原始消息。更多信息可在 [microsoft.com/msrc](https://www.microsoft.com/msrc) 查阅。

请尽可能提供以下所需信息，以帮助我们更好地理解可能问题的性质和范围：

* 问题类型（例如，缓冲区溢出、SQL 注入、跨站脚本攻击等）
* 与问题表现相关的源文件的完整路径
* 受影响源代码的位置（标签/分支/提交或直接 URL）
* 重现问题所需的任何特殊配置
* 重现问题的逐步说明
* 概念验证代码或漏洞利用代码（如果可能）
* 问题的影响，包括攻击者可能如何利用该问题

这些信息将帮助我们更快速地对您的报告进行分类和处理。

如果您是为漏洞奖励计划报告问题，更完整的报告可能会获得更高的奖励金额。有关我们当前活动计划的更多详细信息，请访问 [Microsoft 漏洞奖励计划](https://aka.ms/security.md/msrc/bounty) 页面。

## 优先语言

我们优先使用英语进行所有交流。

## 政策

Microsoft 遵循 [协调漏洞披露](https://aka.ms/security.md/cvd) 原则。

**免责声明**：  
本文件使用基于机器的人工智能翻译服务进行翻译。尽管我们尽力确保准确性，但请注意，自动翻译可能包含错误或不准确之处。原始语言的文件应被视为权威来源。对于关键信息，建议寻求专业人工翻译。对于因使用本翻译而导致的任何误解或误读，我们概不负责。