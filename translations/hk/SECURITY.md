<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T11:29:56+00:00",
  "source_file": "SECURITY.md",
  "language_code": "hk"
}
-->
# 保安

Microsoft 非常重視我們軟件產品和服務的保安，包括所有由我們 GitHub 組織管理的源代碼庫，例如 [Microsoft](https://github.com/Microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet) 和 [Xamarin](https://github.com/xamarin)。

如果你認為在任何 Microsoft 擁有的庫中發現了符合 [Microsoft 的保安漏洞定義](https://aka.ms/security.md/definition) 的漏洞，請按照以下指引向我們報告。

## 報告保安問題

**請勿通過公開的 GitHub 問題報告保安漏洞。**

請改為向 Microsoft Security Response Center (MSRC) 報告，網址為 [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report)。

如果你希望在不登入的情況下提交，可以發送電郵至 [<EMAIL>](mailto:<EMAIL>)。如有可能，請使用我們的 PGP 密鑰加密你的信息；你可以從 [Microsoft Security Response Center PGP Key 頁面](https://aka.ms/security.md/msrc/pgp) 下載密鑰。

你應在 24 小時內收到回覆。如果因某些原因未收到，請通過電郵跟進以確保我們已收到你的原始信息。更多信息可在 [microsoft.com/msrc](https://www.microsoft.com/msrc) 查閱。

請提供以下所需信息（盡可能完整），以幫助我們更好地理解問題的性質和範圍：

* 問題類型（例如：緩衝區溢出、SQL 注入、跨站腳本等）
* 與問題表現相關的源文件的完整路徑
* 受影響源代碼的位置（標籤/分支/提交或直接 URL）
* 重現問題所需的任何特殊配置
* 重現問題的分步指引
* 概念驗證或漏洞代碼（如果可能）
* 問題的影響，包括攻擊者可能如何利用該問題

這些信息將幫助我們更快地分類你的報告。

如果你是為了漏洞獎勵而報告，提交越完整的報告可能會獲得更高的獎勵。請訪問我們的 [Microsoft Bug Bounty Program](https://aka.ms/security.md/msrc/bounty) 頁面了解更多關於現行計劃的詳情。

## 優先語言

我們優先所有以英文進行的溝通。

## 政策

Microsoft 遵循 [協調漏洞披露](https://aka.ms/security.md/cvd) 原則。

**免責聲明**:  
本文件使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。我們致力於提供準確的翻譯，但請注意，自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應被視為權威來源。對於關鍵信息，建議使用專業人工翻譯。我們對因使用此翻譯而產生的任何誤解或誤釋不承擔責任。