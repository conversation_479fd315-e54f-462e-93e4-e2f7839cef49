<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-04-05T19:39:18+00:00",
  "source_file": "SECURITY.md",
  "language_code": "hi"
}
-->
# सुरक्षा

Microsoft हमारे सॉफ़्टवेयर उत्पादों और सेवाओं की सुरक्षा को गंभीरता से लेता है, जिसमें हमारे GitHub संगठनों के माध्यम से प्रबंधित सभी स्रोत कोड रिपॉजिटरी शामिल हैं, जैसे [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) और [Xamarin](https://github.com/xamarin)।

यदि आपको किसी Microsoft-स्वामित्व वाली रिपॉजिटरी में सुरक्षा से संबंधित कोई समस्या मिलती है, जो [Microsoft की सुरक्षा भेद्यता की परिभाषा](https://aka.ms/security.md/definition) के अंतर्गत आती है, तो कृपया इसे नीचे दिए गए निर्देशों के अनुसार रिपोर्ट करें।

## सुरक्षा समस्याओं की रिपोर्टिंग

**कृपया सार्वजनिक GitHub मुद्दों के माध्यम से सुरक्षा भेद्यता की रिपोर्ट न करें।**

इसके बजाय, कृपया Microsoft Security Response Center (MSRC) को [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report) पर रिपोर्ट करें।

यदि आप लॉगिन किए बिना सबमिट करना चाहते हैं, तो [<EMAIL>](mailto:<EMAIL>) पर ईमेल भेजें। यदि संभव हो, तो हमारे PGP कुंजी के साथ अपना संदेश एन्क्रिप्ट करें; इसे [Microsoft Security Response Center PGP Key पृष्ठ](https://aka.ms/security.md/msrc/pgp) से डाउनलोड करें।

आपको 24 घंटे के भीतर उत्तर प्राप्त होना चाहिए। यदि किसी कारण से ऐसा नहीं होता है, तो कृपया ईमेल के माध्यम से फॉलो-अप करें ताकि यह सुनिश्चित हो सके कि हमें आपका मूल संदेश प्राप्त हुआ है। अतिरिक्त जानकारी [microsoft.com/msrc](https://www.microsoft.com/msrc) पर पाई जा सकती है।

कृपया निम्नलिखित जानकारी (जितना संभव हो उतना प्रदान करें) शामिल करें ताकि हमें संभावित समस्या की प्रकृति और दायरे को बेहतर ढंग से समझने में मदद मिल सके:

* समस्या का प्रकार (जैसे बफ़र ओवरफ़्लो, SQL इंजेक्शन, क्रॉस-साइट स्क्रिप्टिंग, आदि)
* संबंधित स्रोत फ़ाइलों के पूर्ण पथ
* प्रभावित स्रोत कोड का स्थान (टैग/ब्रांच/कमिट या सीधा URL)
* समस्या को पुन: उत्पन्न करने के लिए आवश्यक कोई विशेष कॉन्फ़िगरेशन
* समस्या को पुन: उत्पन्न करने के लिए चरण-दर-चरण निर्देश
* प्रूफ़-ऑफ़-कॉन्सेप्ट या एक्सप्लॉइट कोड (यदि संभव हो)
* समस्या का प्रभाव, जिसमें यह भी शामिल है कि हमलावर समस्या का कैसे लाभ उठा सकता है

यह जानकारी हमें आपकी रिपोर्ट को अधिक तेज़ी से प्राथमिकता देने में मदद करेगी।

यदि आप बग बाउंटी के लिए रिपोर्ट कर रहे हैं, तो अधिक संपूर्ण रिपोर्ट उच्च बाउंटी पुरस्कार में योगदान कर सकती हैं। कृपया हमारे सक्रिय कार्यक्रमों के बारे में अधिक जानकारी के लिए [Microsoft Bug Bounty Program](https://aka.ms/security.md/msrc/bounty) पृष्ठ पर जाएं।

## पसंदीदा भाषाएं

हम सभी संचार को अंग्रेजी में रखने को प्राथमिकता देते हैं।

## नीति

Microsoft [Coordinated Vulnerability Disclosure](https://aka.ms/security.md/cvd) के सिद्धांत का पालन करता है।

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता सुनिश्चित करने का प्रयास करते हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियां या अशुद्धियां हो सकती हैं। मूल दस्तावेज़, जो इसकी मूल भाषा में है, को आधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सिफारिश की जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम उत्तरदायी नहीं हैं।