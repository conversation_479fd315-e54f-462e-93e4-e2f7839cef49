<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T11:30:10+00:00",
  "source_file": "SECURITY.md",
  "language_code": "pt"
}
-->
# Segurança

A Microsoft leva a segurança de seus produtos e serviços de software muito a sério, incluindo todos os repositórios de código-fonte gerenciados por meio de nossas organizações no GitHub, que incluem [Microsoft](https://github.com/Microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet) e [Xamarin](https://github.com/xamarin).

Se você acredita ter encontrado uma vulnerabilidade de segurança em qualquer repositório pertencente à Microsoft que atenda à [definição de vulnerabilidade de segurança da Microsoft](https://aka.ms/security.md/definition), por favor, reporte-a conforme descrito abaixo.

## Reportando Problemas de Segurança

**Por favor, não reporte vulnerabilidades de segurança por meio de issues públicas no GitHub.**

Em vez disso, reporte-as ao Microsoft Security Response Center (MSRC) em [https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report).

Se preferir enviar sem fazer login, envie um e-mail para [<EMAIL>](mailto:<EMAIL>). Se possível, criptografe sua mensagem com nossa chave PGP; faça o download na [página da chave PGP do Microsoft Security Response Center](https://aka.ms/security.md/msrc/pgp).

Você deverá receber uma resposta dentro de 24 horas. Caso não receba, por favor, envie um e-mail de acompanhamento para garantir que recebemos sua mensagem original. Informações adicionais podem ser encontradas em [microsoft.com/msrc](https://www.microsoft.com/msrc).

Inclua as informações solicitadas abaixo (o máximo que puder fornecer) para nos ajudar a entender melhor a natureza e o alcance do possível problema:

* Tipo de problema (por exemplo, buffer overflow, injeção de SQL, cross-site scripting, etc.)
* Caminhos completos dos arquivos fonte relacionados à manifestação do problema
* A localização do código fonte afetado (tag/branch/commit ou URL direto)
* Qualquer configuração especial necessária para reproduzir o problema
* Instruções passo a passo para reproduzir o problema
* Código de prova de conceito ou exploit (se possível)
* Impacto do problema, incluindo como um atacante poderia explorá-lo

Essas informações nos ajudarão a priorizar seu relatório mais rapidamente.

Se você estiver reportando para um programa de recompensa por bugs, relatórios mais completos podem contribuir para um prêmio maior. Por favor, visite a página do [Programa de Recompensa por Bugs da Microsoft](https://aka.ms/security.md/msrc/bounty) para mais detalhes sobre nossos programas ativos.

## Idiomas Preferidos

Preferimos que todas as comunicações sejam feitas em inglês.

## Política

A Microsoft segue o princípio de [Divulgação Coordenada de Vulnerabilidades](https://aka.ms/security.md/cvd).

**Aviso Legal**:  
Este documento foi traduzido utilizando o serviço de tradução por IA [Co-op Translator](https://github.com/Azure/co-op-translator). Embora nos esforcemos para garantir a precisão, esteja ciente de que traduções automatizadas podem conter erros ou imprecisões. O documento original em seu idioma nativo deve ser considerado a fonte autoritativa. Para informações críticas, recomenda-se a tradução profissional humana. Não nos responsabilizamos por quaisquer mal-entendidos ou interpretações incorretas decorrentes do uso desta tradução.