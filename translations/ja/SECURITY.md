<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0f93a8c33466486d1e7a0837ca8672ec",
  "translation_date": "2025-03-28T11:30:56+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ja"
}
-->
# セキュリティ

Microsoftは、GitHubの組織を通じて管理されているすべてのソースコードリポジトリを含む、ソフトウェア製品とサービスのセキュリティを重視しています。[Microsoft](https://github.com/Microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet)、[Xamarin](https://github.com/xamarin)などが含まれます。

もし、[Microsoftのセキュリティ脆弱性の定義](https://aka.ms/security.md/definition)に該当する脆弱性をMicrosoftが所有するリポジトリで発見した場合、以下の手順に従って報告してください。

## セキュリティ問題の報告

**セキュリティ脆弱性を公開のGitHubのIssueで報告しないでください。**

代わりに、Microsoft Security Response Center (MSRC)に以下のリンクから報告してください：[https://msrc.microsoft.com/create-report](https://aka.ms/security.md/msrc/create-report)。

ログインせずに報告したい場合は、[<EMAIL>](mailto:<EMAIL>)にメールを送信してください。可能であれば、メッセージをPGPキーで暗号化してください。PGPキーは[Microsoft Security Response Center PGP Keyページ](https://aka.ms/security.md/msrc/pgp)からダウンロードできます。

通常24時間以内に返信を受け取ることができます。もし返信がない場合は、メールで追跡し、最初のメッセージが届いているか確認してください。追加情報については、[microsoft.com/msrc](https://www.microsoft.com/msrc)をご覧ください。

可能な限り以下の情報を含めてください。これにより、問題の性質や範囲をより深く理解する助けとなります：

* 問題の種類（例: バッファオーバーフロー、SQLインジェクション、クロスサイトスクリプティングなど）
* 問題の発生に関連するソースファイルの完全なパス
* 影響を受けたソースコードの場所（タグ/ブランチ/コミットまたは直接のURL）
* 問題を再現するために必要な特別な設定
* 問題を再現するための手順
* 証拠となる概念実証やエクスプロイトコード（可能であれば）
* 問題の影響と攻撃者がその問題をどのように利用する可能性があるか

これらの情報は、報告内容の優先順位付けを迅速に行うために役立ちます。

バグバウンティの報告を行う場合、より詳細な報告を提供することで高額なバウンティ報酬につながる可能性があります。現在のプログラムについての詳細は、[Microsoft Bug Bounty Program](https://aka.ms/security.md/msrc/bounty)ページをご覧ください。

## 推奨言語

すべてのコミュニケーションは英語で行うことを推奨します。

## ポリシー

Microsoftは[Coordinated Vulnerability Disclosure](https://aka.ms/security.md/cvd)の原則に従っています。

**免責事項**:  
この文書は、AI翻訳サービス [Co-op Translator](https://github.com/Azure/co-op-translator) を使用して翻訳されています。正確性を追求していますが、自動翻訳には誤りや不正確さが含まれる可能性があります。元の言語で作成された文書が公式な情報源とみなされるべきです。重要な情報については、専門の人間による翻訳を推奨します。この翻訳の使用に起因する誤解や誤った解釈について、当社は責任を負いません。