{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Semantic Kernel Tool Use Example "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import the Needed Packages "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "from IPython.display import display, HTML\n", "\n", "from typing import Annotated\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import FunctionCallContent, FunctionResultContent, StreamingTextContent\n", "from semantic_kernel.functions import kernel_function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Plugins    \n", "Semantic Kernel uses plugins as tools that can be called by the agent. A plugin can have multiple `kernel_functions` in it as a group. \n", "\n", "In the example below, we create a `DestinationsPlugin` that has two functions: \n", "1. Provides a list of destinations using the `get_destinations` function\n", "2. Provides a list of availability for each destination using the `get_availabilty` function, "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a sample plugin for the sample\n", "class DestinationsPlugin:\n", "    \"\"\"A List of Destinations for vacation.\"\"\"\n", "\n", "    @kernel_function(description=\"Provides a list of vacation destinations.\")\n", "    def get_destinations(self) -> Annotated[str, \"Returns the vacation destinations.\"]:\n", "        return \"\"\"\n", "        Barcelona, Spain\n", "        Paris, France\n", "        Berlin, Germany\n", "        Tokyo, Japan\n", "        New York, USA\n", "        \"\"\"\n", "\n", "    @kernel_function(description=\"Provides the availability of a destination.\")\n", "    def get_availability(\n", "        self, destination: Annotated[str, \"The destination to check availability for.\"]\n", "    ) -> Annotated[str, \"Returns the availability of the destination.\"]:\n", "        return \"\"\"\n", "        Barcelona - Unavailable\n", "        Paris - Available\n", "        Berlin - Available\n", "        Tokyo - Unavailable\n", "        New York - Available\n", "        \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Client\n", "\n", "In this sample, we will use [GitHub Models](https://aka.ms/ai-agents-beginners/github-models) for access to the LLM. \n", "\n", "The `ai_model_id` is defined as `gpt-4o-mini`. Try changing the model to another model available on the GitHub Models marketplace to see the different results. \n", "\n", "For us to use the `Azure Inference SDK` that is used for the `base_url` for GitHub Models, we will use the `OpenAIChatCompletion` connector within Semantic Kernel. There are also other [available connectors](https://learn.microsoft.com/semantic-kernel/concepts/ai-services/chat-completion) to use Semantic Kernel for other model providers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "client = AsyncOpenAI(\n", "    api_key=os.getenv(\"GITHUB_TOKEN\"), \n", "    base_url=\"https://models.inference.ai.azure.com/\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Agent \n", "Now we will create the Agent by using the Agent Name and Instructions that we can set. \n", "\n", "You can change these settings to see how the differences in the agent's response. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the agent\n", "agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    name=\"TravelAgent\",\n", "    instructions=\"Answer questions about the travel destinations and their availability.\",\n", "    plugins=[DestinationsPlugin()],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the Agent \n", "\n", "Now we wil run the AI Agent. In this snippet, we can add two messages to the `user_input` to show how the agent responds to followup questions. \n", "\n", "The agent should call the correct function to get the list of available destinations and confirm the availability of a certain location. \n", "\n", "You can change the `user_inputs` to see how the agent responds. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_inputs = [\n", "    \"What destinations are available?\",\n", "    \"Is Barcelona available?\",\n", "    \"Are there any vacation destinations available not in Europe?\",\n", "]\n", "\n", "async def main():\n", "    thread: ChatHistoryAgentThread | None = None\n", "\n", "    for user_input in user_inputs:\n", "        html_output = (\n", "            f\"<div style='margin-bottom:10px'>\"\n", "            f\"<div style='font-weight:bold'>User:</div>\"\n", "            f\"<div style='margin-left:20px'>{user_input}</div></div>\"\n", "        )\n", "\n", "        agent_name = None\n", "        full_response: list[str] = []\n", "        function_calls: list[str] = []\n", "\n", "        # Buffer to reconstruct streaming function call\n", "        current_function_name = None\n", "        argument_buffer = \"\"\n", "\n", "        async for response in agent.invoke_stream(\n", "            messages=user_input,\n", "            thread=thread,\n", "        ):\n", "            thread = response.thread\n", "            agent_name = response.name\n", "            content_items = list(response.items)\n", "\n", "            for item in content_items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    if item.function_name:\n", "                        current_function_name = item.function_name\n", "\n", "                    # Accumulate arguments (streamed in chunks)\n", "                    if isinstance(item.arguments, str):\n", "                        argument_buffer += item.arguments\n", "                elif isinstance(item, FunctionResultContent):\n", "                    # Finalize any pending function call before showing result\n", "                    if current_function_name:\n", "                        formatted_args = argument_buffer.strip()\n", "                        try:\n", "                            parsed_args = json.loads(formatted_args)\n", "                            formatted_args = json.dumps(parsed_args)\n", "                        except Exception:\n", "                            pass  # leave as raw string\n", "\n", "                        function_calls.append(f\"Calling function: {current_function_name}({formatted_args})\")\n", "                        current_function_name = None\n", "                        argument_buffer = \"\"\n", "\n", "                    function_calls.append(f\"\\nFunction Result:\\n\\n{item.result}\")\n", "                elif isinstance(item, StreamingTextContent) and item.text:\n", "                    full_response.append(item.text)\n", "\n", "        if function_calls:\n", "            html_output += (\n", "                \"<div style='margin-bottom:10px'>\"\n", "                \"<details>\"\n", "                \"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"\n", "                \"<div style='margin:10px; padding:10px; background-color:#f8f8f8; \"\n", "                \"border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"\n", "                f\"{chr(10).join(function_calls)}\"\n", "                \"</div></details></div>\"\n", "            )\n", "\n", "        html_output += (\n", "            \"<div style='margin-bottom:20px'>\"\n", "            f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"\n", "            f\"<div style='margin-left:20px; white-space:pre-wrap'>{''.join(full_response)}</div></div><hr>\"\n", "        )\n", "\n", "        display(HTML(html_output))\n", "\n", "await main()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}