{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RAG Using Azure AI Agent Service & Semantic Kernel\n", "\n", "This code snippet demonstrates how to create and manage an Azure AI agent for retrieval-augmented generation (RAG) using the `Azure AI Agent Service` and `Semantic Kernel`. The agent processes user queries based on the retrieved context and provides accurate responses accordingly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initializing the Environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["SQLite Version Fix\n", "If you encounter the error:\n", "```\n", "RuntimeError: Your system has an unsupported version of sqlite3. Chroma requires sqlite3 >= 3.35.0\n", "```\n", "\n", "Uncomment this code block at the start of your notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install pysqlite3-binary\n", "# __import__('pysqlite3')\n", "# import sys\n", "# sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing Packages\n", "The following code imports the necessary packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Azure imports for project client and credentials\n", "from azure.ai.projects.models import FileSearchTool, OpenAIFile, VectorStore\n", "from azure.identity.aio import DefaultAzureCredential\n", "\n", "# Semantic Kernel imports\n", "from semantic_kernel.agents import AzureAIAgent, AzureAIAgentThread"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Retrieval-Augmented Generation with Semantic Kernel & Azure AI Agent Service\n", "\n", "This sample demonstrates how to use the **Azure AI Agent Service** to perform **Retrieval-Augmented Generation (RAG)** by combining a language model with domain-specific context from an uploaded document.\n", "\n", "### How It Works\n", "\n", "1. **Document Upload**: A markdown file (document.md) containing information (Contoso's travel insurance policy) is uploaded to the agent service.\n", "\n", "2. **Vector Store Creation**: The document is indexed into a vector store to enable semantic search over its contents.\n", "\n", "3. **Agent Configuration**: An agent is instantiated using the `gpt-4o` model with the following strict instructions:\n", "   - Only answer questions based on retrieved content from the document.\n", "   - Decline to answer if the question is out of scope.\n", "\n", "4. **File Search Tool Integration**: The `FileSearchTool` is registered with the agent, enabling the model to search and retrieve relevant snippets from the indexed document during inference.\n", "\n", "5. **User Interaction**: Users can ask questions. If relevant information is found in the document, the agent generates a grounded answer.  \n", "   If not, the agent explicitly responds that the document does not contain sufficient information.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Main Function\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Make sure to first run `az login` using the Azure CLI so that the proper authentication context is provided while using the `DefaultAzureCredential`. The Azure AI Agent Service does not use API keys."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def main():\n", "    async with (\n", "        DefaultAzureCredential() as creds,\n", "        AzureAIAgent.create_client(credential=creds) as client,\n", "    ):\n", "        file: OpenAIFile = await client.agents.upload_file_and_poll(file_path=\"document.md\", purpose=\"assistants\")\n", "        vector_store: VectorStore = await client.agents.create_vector_store_and_poll(\n", "            file_ids=[file.id], name=\"my_vectorstore\"\n", "        )\n", "\n", "        # Define agent name and instructions tailored for RAG.\n", "        AGENT_NAME = \"RAGAgent\"\n", "        AGENT_INSTRUCTIONS = \"\"\"\n", "        You are an AI assistant designed to answer user questions using only the information retrieved from the provided document(s).\n", "\n", "        - If a user's question cannot be answered using the retrieved context, **you must clearly respond**: \n", "        \"I'm sorry, but the uploaded document does not contain the necessary information to answer that question.\"\n", "        - Do not answer from general knowledge or reasoning. Do not make assumptions or generate hypothetical explanations.\n", "        - Do not provide definitions, tutorials, or commentary that is not explicitly grounded in the content of the uploaded file(s).\n", "        - If a user asks a question like \"What is a Neural Network?\", and this is not discussed in the uploaded document, respond as instructed above.\n", "        - For questions that do have relevant content in the document (e.g., Contoso's travel insurance coverage), respond accurately, and cite the document explicitly.\n", "\n", "        You must behave as if you have no external knowledge beyond what is retrieved from the uploaded document.\n", "        \"\"\"\n", "\n", "        \n", "        # Create file search tool with uploaded resources\n", "        file_search = FileSearchTool(vector_store_ids=[vector_store.id])\n", "\n", "        # 3. Create an agent on the Azure AI agent service with the file search tool\n", "        agent_definition = await client.agents.create_agent(\n", "            model=\"gpt-4o\",  # This model should match your Azure OpenAI deployment.\n", "            name=AGENT_NAME,\n", "            instructions=AGENT_INSTRUCTIONS,\n", "            tools=file_search.definitions,\n", "            tool_resources=file_search.resources,\n", "        )\n", "        \n", "        # Create the Azure AI Agent using the client and definition.\n", "        agent = AzureAIAgent(\n", "            client=client,\n", "            definition=agent_definition,\n", "        )\n", "        \n", "        # Create a thread to hold the conversation\n", "        # If no thread is provided, a new thread will be\n", "        # created and returned with the initial response\n", "        thread: AzureAIAgentThread | None = None\n", "        \n", "        # Example user queries.\n", "        user_inputs = [\n", "            \"Can you explain Contoso's travel insurance coverage?\",  # Relevant context.\n", "            \"What is a Neural Network?\"  # No relevant context from the document. Will not contain a source annotation.\n", "        ]\n", "        \n", "        try:\n", "            for user_input in user_inputs:\n", "                print(f\"# User: '{user_input}'\")\n", "                # Invoke the agent for the specified thread for response\n", "                async for response in agent.invoke(messages=user_input, thread=thread):\n", "                    print(f\"# {response.name}: {response}\")\n", "                    thread = response.thread\n", "        finally:\n", "            # Clean up resources.\n", "            await thread.delete() if thread else None\n", "            await client.agents.delete_vector_store(vector_store.id)\n", "            await client.agents.delete_file(file.id)\n", "            await client.agents.delete_agent(agent.id)\n", "            print(\"\\nCleaned up agent, thread, file, and vector store.\")\n", "\n", "await main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You should see output similar to:\n", "\n", "```\n", "# User: 'Can you explain Contoso's travel insurance coverage?'\n", "# Agent: <PERSON><PERSON><PERSON>'s travel insurance coverage includes protection for medical emergencies, trip cancellations, and lost baggage【4:0†document.md】.\n", "# User: 'What is a Neural Network?'\n", "# Agent: I'm sorry, but the uploaded document does not contain the necessary information to answer that question.\n", "\n", "Cleaned up agent, thread, file, and vector store.\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}