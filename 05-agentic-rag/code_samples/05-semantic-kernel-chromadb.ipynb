{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Semantic Kernel Tool Use Example\n", "\n", "This document provides an overview and explanation of the code used to create a Semantic Kernel-based tool that integrates with ChromaDB for Retrieval-Augmented Generation (RAG). The example demonstrates how to build an AI agent that retrieves travel documents from a ChromaDB collection, augments user queries with semantic search results, and streams detailed travel recommendations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initializing the Environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["SQLite Version Fix\n", "If you encounter the error:\n", "```\n", "RuntimeError: Your system has an unsupported version of sqlite3. Chroma requires sqlite3 >= 3.35.0\n", "```\n", "\n", "Uncomment this code block at the start of your notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install pysqlite3-binary\n", "# import sys\n", "# sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing Packages\n", "The following code imports the necessary packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import chromadb\n", "from typing import Annotated, TYPE_CHECKING\n", "\n", "from IPython.display import display, HTML\n", "\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import FunctionCallContent,FunctionResultContent, StreamingTextContent\n", "from semantic_kernel.functions import kernel_function\n", "\n", "if TYPE_CHECKING:\n", "    from chromadb.api.models.Collection import Collection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating the Semantic Kernel and AI Service\n", "\n", "A Semantic Kernel instance is created and configured with an asynchronous OpenAI chat completion service. The service is added to the kernel for use in generating responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the asynchronous OpenAI client\n", "client = AsyncOpenAI(\n", "    api_key=os.environ[\"GITHUB_TOKEN\"],\n", "    base_url=\"https://models.inference.ai.azure.com/\"\n", ")\n", "\n", "\n", "# Create the OpenAI Chat Completion Service\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining the Prompt Plugin\n", "\n", "The PromptPlugin is a native plugin that defines a function to build an augmented prompt using retrieval context"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PromptPlugin:\n", "\n", "    def __init__(self, collection: \"Collection\"):\n", "        self.collection = collection\n", "\n", "    @kernel_function(\n", "        name=\"build_augmented_prompt\",\n", "        description=\"Build an augmented prompt using retrieval context.\"\n", "    )\n", "    def build_augmented_prompt(self, query: str, retrieval_context: str) -> str:\n", "        return (\n", "            f\"Retrieved Context:\\n{retrieval_context}\\n\\n\"\n", "            f\"User Query: {query}\\n\\n\"\n", "            \"Based ONLY on the above context, please provide your answer.\"\n", "        )\n", "    \n", "    @kernel_function(name=\"retrieve_context\", description=\"Retrieve context from the database.\")\n", "    def get_retrieval_context(self, query: str) -> str:\n", "        results = self.collection.query(\n", "            query_texts=[query],\n", "            include=[\"documents\", \"metadatas\"],\n", "            n_results=2\n", "        )\n", "        context_entries = []\n", "        if results and results.get(\"documents\") and results[\"documents\"][0]:\n", "            for doc, meta in zip(results[\"documents\"][0], results[\"metadatas\"][0]):\n", "                context_entries.append(f\"Document: {doc}\\nMetadata: {meta}\")\n", "        return \"\\n\\n\".join(context_entries) if context_entries else \"No retrieval context found.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Weather Information Plugin\n", "\n", "The WeatherInfoPlugin is a native plugin that provides temperature information for specific travel destinations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class WeatherInfoPlugin:\n", "    \"\"\"A Plugin that provides the average temperature for a travel destination.\"\"\"\n", "\n", "    def __init__(self):\n", "        # Dictionary of destinations and their average temperatures\n", "        self.destination_temperatures = {\n", "            \"maldives\": \"82°F (28°C)\",\n", "            \"swiss alps\": \"45°F (7°C)\",\n", "            \"african safaris\": \"75°F (24°C)\"\n", "        }\n", "\n", "    @kernel_function(description=\"Get the average temperature for a specific travel destination.\")\n", "    def get_destination_temperature(self, destination: str) -> Annotated[str, \"Returns the average temperature for the destination.\"]:\n", "        \"\"\"Get the average temperature for a travel destination.\"\"\"\n", "        # Normalize the input destination (lowercase)\n", "        normalized_destination = destination.lower()\n", "\n", "        # Look up the temperature for the destination\n", "        if normalized_destination in self.destination_temperatures:\n", "            return f\"The average temperature in {destination} is {self.destination_temperatures[normalized_destination]}.\"\n", "        else:\n", "            return f\"Sorry, I don't have temperature information for {destination}. Available destinations are: Maldives, Swiss Alps, and African safaris.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Destinations Information Plugin\n", "\n", "The DestinationsPlugin is a native plugin that provides detailed information about popular travel destinations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DestinationsPlugin:\n", "    # Destination data store with rich details about popular travel locations\n", "    DESTINATIONS = {\n", "        \"maldives\": {\n", "            \"name\": \"The Maldives\",\n", "            \"description\": \"An archipelago of 26 atolls in the Indian Ocean, known for pristine beaches and overwater bungalows.\",\n", "            \"best_time\": \"November to April (dry season)\",\n", "            \"activities\": [\"Snorkeling\", \"Diving\", \"Island hopping\", \"Spa retreats\", \"Underwater dining\"],\n", "            \"avg_cost\": \"$400-1200 per night for luxury resorts\"\n", "        },\n", "        \"swiss alps\": {\n", "            \"name\": \"The Swiss Alps\",\n", "            \"description\": \"Mountain range spanning across Switzerland with picturesque villages and world-class ski resorts.\",\n", "            \"best_time\": \"December to March for skiing, June to September for hiking\",\n", "            \"activities\": [\"Skiing\", \"Snowboarding\", \"Hiking\", \"Mountain biking\", \"Paragliding\"],\n", "            \"avg_cost\": \"$250-500 per night for alpine accommodations\"\n", "        },\n", "        \"safari\": {\n", "            \"name\": \"African Safari\",\n", "            \"description\": \"Wildlife viewing experiences across various African countries including Kenya, Tanzania, and South Africa.\",\n", "            \"best_time\": \"June to October (dry season) for optimal wildlife viewing\",\n", "            \"activities\": [\"Game drives\", \"Walking safaris\", \"Hot air balloon rides\", \"Cultural village visits\"],\n", "            \"avg_cost\": \"$400-800 per person per day for luxury safari packages\"\n", "        },\n", "        \"bali\": {\n", "            \"name\": \"Bali, Indonesia\",\n", "            \"description\": \"Island paradise known for lush rice terraces, beautiful temples, and vibrant culture.\",\n", "            \"best_time\": \"April to October (dry season)\",\n", "            \"activities\": [\"Surfing\", \"Temple visits\", \"Rice terrace trekking\", \"Yoga retreats\", \"Beach relaxation\"],\n", "            \"avg_cost\": \"$100-500 per night depending on accommodation type\"\n", "        },\n", "        \"santorini\": {\n", "            \"name\": \"Santorini, Greece\",\n", "            \"description\": \"Stunning volcanic island with white-washed buildings and blue domes overlooking the Aegean Sea.\",\n", "            \"best_time\": \"Late April to early November\",\n", "            \"activities\": [\"Sunset watching in Oia\", \"Wine tasting\", \"Boat tours\", \"Beach hopping\", \"Ancient ruins exploration\"],\n", "            \"avg_cost\": \"$200-600 per night for caldera view accommodations\"\n", "        }\n", "    }\n", "\n", "    @kernel_function(\n", "        name=\"get_destination_info\",\n", "        description=\"Provides detailed information about specific travel destinations.\"\n", "    )\n", "    def get_destination_info(self, query: str) -> str:\n", "        # Find which destination is being asked about\n", "        query_lower = query.lower()\n", "        matching_destinations = []\n", "\n", "        for key, details in DestinationsPlugin.DESTINATIONS.items():\n", "            if key in query_lower or details[\"name\"].lower() in query_lower:\n", "                matching_destinations.append(details)\n", "\n", "        if not matching_destinations:\n", "            return (f\"User Query: {query}\\n\\n\"\n", "                    f\"I couldn't find specific destination information in our database. \"\n", "                    f\"Please use the general retrieval system for this query.\")\n", "\n", "        # Format destination information\n", "        destination_info = \"\\n\\n\".join([\n", "            f\"Destination: {dest['name']}\\n\"\n", "            f\"Description: {dest['description']}\\n\"\n", "            f\"Best time to visit: {dest['best_time']}\\n\"\n", "            f\"Popular activities: {', '.join(dest['activities'])}\\n\"\n", "            f\"Average cost: {dest['avg_cost']}\" for dest in matching_destinations\n", "        ])\n", "\n", "        return (f\"Destination Information:\\n{destination_info}\\n\\n\"\n", "                f\"User Query: {query}\\n\\n\"\n", "                \"Based on the above destination details, provide a helpful response \"\n", "                \"that addresses the user's query about this location.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up ChromaDB\n", "\n", "To facilitate Retrieval-Augmented Generation, a persistent ChromaDB client is instantiated and a collection named `\"travel_documents\"` is created (or retrieved if it exists). This collection is then populated with sample travel documents and metadata."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["collection = chromadb.PersistentClient(path=\"./chroma_db\").create_collection(\n", "    name=\"travel_documents\",\n", "    metadata={\"description\": \"travel_service\"},\n", "    get_or_create=True,\n", ")\n", "\n", "documents = [\n", "    \"Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\",\n", "    \"Our premium travel services include personalized itinerary planning and 24/7 concierge support.\",\n", "    \"Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\",\n", "    \"Popular destinations include the Maldives, Swiss Alps, and African safaris.\",\n", "    \"Contoso Travel provides exclusive access to boutique hotels and private guided tours.\",\n", "]\n", "\n", "collection.add(\n", "    documents=documents,\n", "    ids=[f\"doc_{i}\" for i in range(len(documents))],\n", "    metadatas=[{\"source\": \"training\", \"type\": \"explanation\"} for _ in documents]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    plugins=[DestinationsPlugin(), WeatherInfoPlugin(), PromptPlugin(collection)],\n", "    name=\"TravelAgent\",\n", "    instructions=\"Answer travel queries using the provided context. If context is provided, do not say 'I have no context for that.'\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running the Agent with Streaming Chat History\n", "The main asynchronous loop creates a chat history for the conversation and, for each user input, first adds the augmented prompt (as a system message) to the chat history so that the agent sees the retrieval context. The user message is also added, and then the agent is invoked using streaming. The output is printed as it streams in."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def main():\n", "    thread: ChatHistoryAgentThread | None = None\n", "\n", "    user_inputs = [\n", "        \"Can you explain Contoso's travel insurance coverage?\",\n", "        \"What is the average temperature of the Maldives?\",\n", "        \"What is a good cold destination offered by Contoso and what is it average temperature?\",\n", "    ]\n", "\n", "    for user_input in user_inputs:\n", "        html_output = (\n", "            f\"<div style='margin-bottom:10px'>\"\n", "            f\"<div style='font-weight:bold'>User:</div>\"\n", "            f\"<div style='margin-left:20px'>{user_input}</div></div>\"\n", "        )\n", "\n", "        agent_name = None\n", "        full_response: list[str] = []\n", "        function_calls: list[str] = []\n", "\n", "        # Buffer to reconstruct streaming function call\n", "        current_function_name = None\n", "        argument_buffer = \"\"\n", "\n", "        async for response in agent.invoke_stream(\n", "            messages=user_input,\n", "            thread=thread,\n", "        ):\n", "            thread = response.thread\n", "            agent_name = response.name\n", "            content_items = list(response.items)\n", "\n", "            for item in content_items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    if item.function_name:\n", "                        current_function_name = item.function_name\n", "\n", "                    # Accumulate arguments (streamed in chunks)\n", "                    if isinstance(item.arguments, str):\n", "                        argument_buffer += item.arguments\n", "                elif isinstance(item, FunctionResultContent):\n", "                    # Finalize any pending function call before showing result\n", "                    if current_function_name:\n", "                        formatted_args = argument_buffer.strip()\n", "                        try:\n", "                            parsed_args = json.loads(formatted_args)\n", "                            formatted_args = json.dumps(parsed_args)\n", "                        except Exception:\n", "                            pass  # leave as raw string\n", "\n", "                        function_calls.append(f\"Calling function: {current_function_name}({formatted_args})\")\n", "                        current_function_name = None\n", "                        argument_buffer = \"\"\n", "\n", "                    function_calls.append(f\"\\nFunction Result:\\n\\n{item.result}\")\n", "                elif isinstance(item, StreamingTextContent) and item.text:\n", "                    full_response.append(item.text)\n", "\n", "        if function_calls:\n", "            html_output += (\n", "                \"<div style='margin-bottom:10px'>\"\n", "                \"<details>\"\n", "                \"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"\n", "                \"<div style='margin:10px; padding:10px; background-color:#f8f8f8; \"\n", "                \"border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"\n", "                f\"{chr(10).join(function_calls)}\"\n", "                \"</div></details></div>\"\n", "            )\n", "\n", "        html_output += (\n", "            \"<div style='margin-bottom:20px'>\"\n", "            f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"\n", "            f\"<div style='margin-left:20px; white-space:pre-wrap'>{''.join(full_response)}</div></div><hr>\"\n", "        )\n", "\n", "        display(HTML(html_output))\n", "\n", "await main()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}