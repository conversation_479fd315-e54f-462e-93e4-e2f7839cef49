{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agentic RAG with Autogen using Azure AI Services\n", "\n", "This notebook demonstrates implementing Retrieval-Augmented Generation (RAG) using Autogen agents with enhanced evaluation capabilities."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import time\n", "import asyncio\n", "from typing import List, Dict\n", "\n", "from autogen_agentchat.agents import AssistantAgent\n", "from autogen_core import CancellationToken\n", "from autogen_agentchat.messages import TextMessage\n", "from azure.core.credentials import AzureKeyCredential\n", "from autogen_ext.models.azure import AzureAIChatCompletionClient\n", "\n", "from azure.search.documents import SearchClient\n", "from azure.search.documents.indexes import SearchIndexClient\n", "from azure.search.documents.indexes.models import SearchIndex, SimpleField, SearchFieldDataType, SearchableField\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the Client \n", "\n", "First, we initialize the Azure AI Chat Completion Client. This client will be used to interact with the Azure OpenAI service to generate responses to user queries."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["client = AzureAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    endpoint=\"https://models.inference.ai.azure.com\",\n", "    credential=AzureKeyCredential(os.getenv(\"GITHUB_TOKEN\")),\n", "    model_info={\n", "        \"json_output\": True,\n", "        \"function_calling\": True,\n", "        \"vision\": True,\n", "        \"family\": \"unknown\",\n", "    },\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vector Database Initialization\n", "\n", "We initialize Azure AI Search with persistent storage and add enhanced sample documents. Azure AI Search will be used to store and retrieve documents that provide context for generating accurate responses."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<azure.search.documents._generated.models._models_py3.IndexingResult at 0x2299c1b8200>,\n", " <azure.search.documents._generated.models._models_py3.IndexingResult at 0x2299c1b9d90>,\n", " <azure.search.documents._generated.models._models_py3.IndexingResult at 0x2299c1b9bb0>,\n", " <azure.search.documents._generated.models._models_py3.IndexingResult at 0x2299c1b9d00>,\n", " <azure.search.documents._generated.models._models_py3.IndexingResult at 0x2299c1b9c70>]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize Azure AI Search with persistent storage\n", "search_service_endpoint = os.getenv(\"AZURE_SEARCH_SERVICE_ENDPOINT\")\n", "search_api_key = os.getenv(\"AZURE_SEARCH_API_KEY\")\n", "index_name = \"travel-documents\"\n", "\n", "search_client = SearchClient(\n", "    endpoint=search_service_endpoint,\n", "    index_name=index_name,\n", "    credential=AzureKeyCredential(search_api_key)\n", ")\n", "\n", "index_client = SearchIndexClient(\n", "    endpoint=search_service_endpoint,\n", "    credential=AzureKeyCredential(search_api_key)\n", ")\n", "\n", "# Define the index schema\n", "fields = [\n", "    SimpleField(name=\"id\", type=SearchFieldDataType.String, key=True),\n", "    SearchableField(name=\"content\", type=SearchFieldDataType.String)\n", "]\n", "\n", "index = SearchIndex(name=index_name, fields=fields)\n", "\n", "# Create the index\n", "index_client.create_index(index)\n", "\n", "# Enhanced sample documents\n", "documents = [\n", "    {\"id\": \"1\", \"content\": \"Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\"},\n", "    {\"id\": \"2\", \"content\": \"Our premium travel services include personalized itinerary planning and 24/7 concierge support.\"},\n", "    {\"id\": \"3\", \"content\": \"Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\"},\n", "    {\"id\": \"4\", \"content\": \"Popular destinations include the Maldives, Swiss Alps, and African safaris.\"},\n", "    {\"id\": \"5\", \"content\": \"Contoso Travel provides exclusive access to boutique hotels and private guided tours.\"}\n", "]\n", "\n", "# Add documents to the index\n", "search_client.upload_documents(documents)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def get_retrieval_context(query: str) -> str:\n", "    results = search_client.search(query)\n", "    context_strings = []\n", "    for result in results:\n", "        context_strings.append(f\"Document: {result['content']}\")\n", "    return \"\\n\\n\".join(context_strings) if context_strings else \"No results found\"\n", "\n", "def get_weather_data(location: str) -> str:\n", "    \"\"\"\n", "    Simulates retrieving weather data for a given location.\n", "    In a real-world scenario, this would call a weather API.\n", "    \"\"\"\n", "    # Simulated weather data for common locations\n", "    weather_database = {\n", "        \"new york\": {\"temperature\": 72, \"condition\": \"Partly Cloudy\", \"humidity\": 65, \"wind\": \"10 mph\"},\n", "        \"london\": {\"temperature\": 60, \"condition\": \"Rainy\", \"humidity\": 80, \"wind\": \"15 mph\"},\n", "        \"tokyo\": {\"temperature\": 75, \"condition\": \"Sunny\", \"humidity\": 50, \"wind\": \"5 mph\"},\n", "        \"sydney\": {\"temperature\": 80, \"condition\": \"Clear\", \"humidity\": 45, \"wind\": \"12 mph\"},\n", "        \"paris\": {\"temperature\": 68, \"condition\": \"Cloudy\", \"humidity\": 70, \"wind\": \"8 mph\"},\n", "    }\n", "    \n", "    # Normalize the location string\n", "    location_key = location.lower()\n", "    \n", "    # Check if we have data for this location\n", "    if location_key in weather_database:\n", "        data = weather_database[location_key]\n", "        return f\"Weather for {location.title()}:\\n\" \\\n", "               f\"Temperature: {data['temperature']}°F\\n\" \\\n", "               f\"Condition: {data['condition']}\\n\" \\\n", "               f\"Humidity: {data['humidity']}%\\n\" \\\n", "               f\"Wind: {data['wind']}\"\n", "    else:\n", "        return f\"No weather data available for {location}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent Configuration\n", "\n", "We configure the retrieval and assistant agents. The retrieval agent is specialized in finding relevant information using semantic search, while the assistant generates detailed responses based on the retrieved information."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Create agents with enhanced capabilities\n", "assistant = AssistantA<PERSON>(\n", "    name=\"assistant\",\n", "    model_client=client,\n", "    system_message=(\n", "        \"You are a helpful AI assistant that provides answers using ONLY the provided context. \"\n", "        \"Do NOT include any external information. Base your answer entirely on the context given below.\"\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RAGEvaluator Class\n", "\n", "We define the `RAGEvaluator` class to evaluate the response based on various metrics like response length, source citations, response time, and context relevance."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["class RAGEvaluator:\n", "    def __init__(self):\n", "        self.responses: List[Dict] = []\n", "\n", "    def evaluate_response(self, query: str, response: str, context: List[Dict]) -> Dict:\n", "        # Basic metrics: response length, citation count, and a simple relevance score.\n", "        start_time = time.time()\n", "        metrics = {\n", "            'response_length': len(response),\n", "            'source_citations': sum(1 for doc in context if doc[\"content\"] in response),\n", "            'evaluation_time': time.time() - start_time,\n", "            'context_relevance': self._calculate_relevance(query, context)\n", "        }\n", "        self.responses.append({\n", "            'query': query,\n", "            'response': response,\n", "            'metrics': metrics\n", "        })\n", "        return metrics\n", "\n", "    def _calculate_relevance(self, query: str, context: List[Dict]) -> float:\n", "        # Simple relevance score: fraction of the documents where the query appears.\n", "        return sum(1 for c in context if query.lower() in c[\"content\"].lower()) / len(context)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Query Processing with RAG\n", "\n", "We define the `ask_rag` function to send the query to the assistant, process the response, and evaluate it. This function handles the interaction with the assistant and uses the evaluator to measure the quality of the response."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["async def ask_unified_rag(query: str, evaluator: RAGEvaluator, location: str = None):\n", "    \"\"\"\n", "    A unified RAG function that combines both document retrieval and weather data\n", "    based on the query and optional location parameter.\n", "    \n", "    Args:\n", "        query: The user's question\n", "        evaluator: The RAG evaluator to measure response quality\n", "        location: Optional location for weather queries\n", "    \"\"\"\n", "    try:\n", "        # Get context from both sources\n", "        retrieval_context = get_retrieval_context(query)\n", "        \n", "        # If location is provided, add weather data\n", "        weather_context = \"\"\n", "        if location:\n", "            weather_context = get_weather_data(location)\n", "            weather_intro = f\"\\nWeather Information for {location}:\\n\"\n", "        else:\n", "            weather_intro = \"\"\n", "        \n", "        # Augment the query with both contexts if available\n", "        augmented_query = (\n", "            f\"Retrieved Context:\\n{retrieval_context}\\n\\n\"\n", "            f\"{weather_intro}{weather_context}\\n\\n\"\n", "            f\"User Query: {query}\\n\\n\"\n", "            \"Based ONLY on the above context, please provide the answer.\"\n", "        )\n", "\n", "        # Send the augmented query as a user message\n", "        start_time = time.time()\n", "        response = await assistant.on_messages(\n", "            [TextMessage(content=augmented_query, source=\"user\")],\n", "            cancellation_token=CancellationToken(),\n", "        )\n", "        processing_time = time.time() - start_time\n", "\n", "        # Create combined context for evaluation\n", "        combined_context = documents.copy()  # Start with travel documents\n", "        \n", "        # Add weather as a document if it exists\n", "        if location and weather_context:\n", "            combined_context.append({\"id\": f\"weather-{location}\", \"content\": weather_context})\n", "        \n", "        # Evaluate the response\n", "        metrics = evaluator.evaluate_response(\n", "            query=query,\n", "            response=response.chat_message.content,\n", "            context=combined_context\n", "        )\n", "        \n", "        result = {\n", "            'response': response.chat_message.content,\n", "            'processing_time': processing_time,\n", "            'metrics': metrics,\n", "        }\n", "        \n", "        # Add location to result if provided\n", "        if location:\n", "            result['location'] = location\n", "            \n", "        return result\n", "    except Exception as e:\n", "        print(f\"Error processing unified query: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example usage\n", "\n", "We initialize the evaluator and define the queries that we want to process and evaluate."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["async def main():\n", "    evaluator = RAGEvaluator()\n", "    \n", "    # Define user queries similar to the Semantic Kernel example\n", "    user_inputs = [\n", "        # Travel-only queries\n", "        {\"query\": \"Can you explain Contoso's travel insurance coverage?\"},\n", "        \n", "        # Weather-only queries \n", "        {\"query\": \"What's the current weather condition in London?\", \"location\": \"london\"},\n", "        \n", "        # Combined queries\n", "        {\"query\": \"What is a good cold destination offered by Contoso and what is its temperature?\", \"location\": \"london\"},\n", "    ]\n", "    \n", "    print(\"Processing Queries:\")\n", "    for query_data in user_inputs:\n", "        query = query_data[\"query\"]\n", "        location = query_data.get(\"location\")\n", "        \n", "        if location:\n", "            print(f\"\\nProcessing Query for {location}: {query}\")\n", "        else:\n", "            print(f\"\\nProcessing Query: {query}\")\n", "        \n", "        # Get the RAG context for printing (similar to the Semantic Kernel example)\n", "        retrieval_context = get_retrieval_context(query)\n", "        weather_context = get_weather_data(location) if location else \"\"\n", "        \n", "        # Print the RAG context for transparency\n", "        print(\"\\n--- RAG Context ---\")\n", "        print(retrieval_context)\n", "        if weather_context:\n", "            print(f\"\\n--- Weather Context for {location} ---\")\n", "            print(weather_context)\n", "        print(\"-------------------\\n\")\n", "            \n", "        result = await ask_unified_rag(query, evaluator, location)\n", "        if result:\n", "            print(\"Response:\", result['response'])\n", "            print(\"\\nMetrics:\", result['metrics'])\n", "        print(\"\\n\" + \"=\"*60 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Script\n", "\n", "We check if the script is running in an interactive environment or a standard script, and run the main function accordingly."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Queries:\n", "\n", "Processing Query: Can you explain Contoso's travel insurance coverage?\n", "\n", "--- RAG Context ---\n", "Document: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "\n", "Document: Our premium travel services include personalized itinerary planning and 24/7 concierge support.\n", "\n", "Document: Contoso Travel provides exclusive access to boutique hotels and private guided tours.\n", "\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "-------------------\n", "\n", "Response: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "\n", "Metrics: {'response_length': 92, 'source_citations': 1, 'evaluation_time': 0.0, 'context_relevance': 0.0}\n", "\n", "============================================================\n", "\n", "\n", "Processing Query for london: What's the current weather condition in London?\n", "\n", "--- RAG Context ---\n", "Document: Popular destinations include the Maldives, Swiss Alps, and African safaris.\n", "\n", "--- Weather Context for london ---\n", "Weather for London:\n", "Temperature: 60°F\n", "Condition: <PERSON><PERSON>\n", "Humidity: 80%\n", "Wind: 15 mph\n", "-------------------\n", "\n", "Response: The current weather condition in London is rainy.\n", "\n", "Metrics: {'response_length': 49, 'source_citations': 0, 'evaluation_time': 0.0, 'context_relevance': 0.0}\n", "\n", "============================================================\n", "\n", "\n", "Processing Query for london: What is a good cold destination offered by Contoso and what is its temperature?\n", "\n", "--- RAG Context ---\n", "Document: Contoso Travel provides exclusive access to boutique hotels and private guided tours.\n", "\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "\n", "Document: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "\n", "Document: Popular destinations include the Maldives, Swiss Alps, and African safaris.\n", "\n", "Document: Our premium travel services include personalized itinerary planning and 24/7 concierge support.\n", "\n", "--- Weather Context for london ---\n", "Weather for London:\n", "Temperature: 60°F\n", "Condition: <PERSON><PERSON>\n", "Humidity: 80%\n", "Wind: 15 mph\n", "-------------------\n", "\n", "Response: A good cold destination offered by Contoso is the Swiss Alps. However, the temperature for this destination is not provided in the context.\n", "\n", "Metrics: {'response_length': 139, 'source_citations': 0, 'evaluation_time': 0.0, 'context_relevance': 0.0}\n", "\n", "============================================================\n", "\n"]}], "source": ["if __name__ == \"__main__\":\n", "    if asyncio.get_event_loop().is_running():\n", "        await main()\n", "    else:\n", "        asyncio.run(main())"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}