{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agentic RAG with Autogen\n", "\n", "This notebook demonstrates implementing Retrieval-Augmented Generation (RAG) using Autogen agents with enhanced evaluation capabilities."]}, {"cell_type": "markdown", "metadata": {}, "source": ["SQLite Version Fix\n", "If you encounter the error:\n", "```\n", "RuntimeError: Your system has an unsupported version of sqlite3. Chroma requires sqlite3 >= 3.35.0\n", "```\n", "\n", "Uncomment this code block at the start of your notebook:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# %pip install pysqlite3-binary\n", "# __import__('pysqlite3')\n", "# import sys\n", "# sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import time\n", "import asyncio\n", "from typing import List, Dict\n", "from dotenv import load_dotenv\n", "\n", "from autogen_agentchat.agents import AssistantAgent\n", "from autogen_core import CancellationToken\n", "from autogen_agentchat.messages import TextMessage\n", "from azure.core.credentials import AzureKeyCredential\n", "from autogen_ext.models.azure import AzureAIChatCompletionClient\n", "\n", "import chromadb\n", "\n", "load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the Client \n", "\n", "First, we initialize the Azure AI Chat Completion Client. This client will be used to interact with the Azure OpenAI service to generate responses to user queries."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["client = AzureAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    endpoint=\"https://models.inference.ai.azure.com\",\n", "    credential=AzureKeyCredential(os.getenv(\"GITHUB_TOKEN\")),\n", "    model_info={\n", "        \"json_output\": True,\n", "        \"function_calling\": True,\n", "        \"vision\": True,\n", "        \"family\": \"unknown\",\n", "    },\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vector Database Initialization\n", "\n", "We initialize ChromaDB with persistent storage and add enhanced sample documents. ChromaDB will be used to store and retrieve documents that provide context for generating accurate responses."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Initialize ChromaDB with persistent storage\n", "chroma_client = chromadb.PersistentClient(path=\"./chroma_db\")\n", "collection = chroma_client.create_collection(\n", "    name=\"travel_documents\",\n", "    metadata={\"description\": \"travel_service\"},\n", "    get_or_create=True\n", ")\n", "\n", "# Enhanced sample documents\n", "documents = [\n", "    \"Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\",\n", "    \"Our premium travel services include personalized itinerary planning and 24/7 concierge support.\",\n", "    \"Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\",\n", "    \"Popular destinations include the Maldives, Swiss Alps, and African safaris.\",\n", "    \"Contoso Travel provides exclusive access to boutique hotels and private guided tours.\"\n", "]\n", "\n", "# Add documents with metadata\n", "collection.add(\n", "    documents=documents,\n", "    ids=[f\"doc_{i}\" for i in range(len(documents))],\n", "    metadatas=[{\"source\": \"training\", \"type\": \"explanation\"} for _ in documents]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Context Provider Implementation\n", "\n", "The `ContextProvider` class handles context retrieval and integration from multiple sources:\n", "\n", "1. **Vector Database Retrieval**: Uses ChromaDB to perform semantic search on travel documents\n", "2. **Weather Information**: Maintains a simulated weather database for major cities\n", "3. **Unified Context**: Combines both document and weather data into comprehensive context\n", "\n", "Key methods:\n", "- `get_retrieval_context()`: Retrieves relevant documents based on query\n", "- `get_weather_data()`: Provides weather information for specified locations\n", "- `get_unified_context()`: Combines both document and weather contexts for enhanced responses"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class ContextProvider:\n", "    def __init__(self, collection):\n", "        self.collection = collection\n", "        # Simulated weather database\n", "        self.weather_database = {\n", "            \"new york\": {\"temperature\": 72, \"condition\": \"Partly Cloudy\", \"humidity\": 65, \"wind\": \"10 mph\"},\n", "            \"london\": {\"temperature\": 60, \"condition\": \"Rainy\", \"humidity\": 80, \"wind\": \"15 mph\"},\n", "            \"tokyo\": {\"temperature\": 75, \"condition\": \"Sunny\", \"humidity\": 50, \"wind\": \"5 mph\"},\n", "            \"sydney\": {\"temperature\": 80, \"condition\": \"Clear\", \"humidity\": 45, \"wind\": \"12 mph\"},\n", "            \"paris\": {\"temperature\": 68, \"condition\": \"Cloudy\", \"humidity\": 70, \"wind\": \"8 mph\"},\n", "        }\n", "    \n", "    def get_retrieval_context(self, query: str) -> str:\n", "        \"\"\"Retrieves relevant documents from vector database based on query.\"\"\"\n", "        results = self.collection.query(\n", "            query_texts=[query],\n", "            include=[\"documents\", \"metadatas\"],\n", "            n_results=2\n", "        )\n", "        context_strings = []\n", "        if results and results.get(\"documents\") and len(results[\"documents\"][0]) > 0:\n", "            for doc, meta in zip(results[\"documents\"][0], results[\"metadatas\"][0]):\n", "                context_strings.append(f\"Document: {doc}\\nMetadata: {meta}\")\n", "        return \"\\n\\n\".join(context_strings) if context_strings else \"No relevant documents found\"\n", "    \n", "    def get_weather_data(self, location: str) -> str:\n", "        \"\"\"Simulates retrieving weather data for a given location.\"\"\"\n", "        if not location:\n", "            return \"\"\n", "            \n", "        location_key = location.lower()\n", "        if location_key in self.weather_database:\n", "            data = self.weather_database[location_key]\n", "            return f\"Weather for {location.title()}:\\n\" \\\n", "                   f\"Temperature: {data['temperature']}°F\\n\" \\\n", "                   f\"Condition: {data['condition']}\\n\" \\\n", "                   f\"Humidity: {data['humidity']}%\\n\" \\\n", "                   f\"Wind: {data['wind']}\"\n", "        else:\n", "            return f\"No weather data available for {location}.\"\n", "    \n", "    def get_unified_context(self, query: str, location: str = None) -> str:\n", "        \"\"\"Returns a unified context combining both document retrieval and weather data.\"\"\"\n", "        retrieval_context = self.get_retrieval_context(query)\n", "        \n", "        weather_context = \"\"\n", "        if location:\n", "            weather_context = self.get_weather_data(location)\n", "            weather_intro = f\"\\nWeather Information for {location}:\\n\"\n", "        else:\n", "            weather_intro = \"\"\n", "        \n", "        return f\"Retrieved Context:\\n{retrieval_context}\\n\\n{weather_intro}{weather_context}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent Configuration\n", "\n", "We configure the retrieval and assistant agents. The retrieval agent is specialized in finding relevant information using semantic search, while the assistant generates detailed responses based on the retrieved information."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Create agents with enhanced capabilities\n", "assistant = AssistantA<PERSON>(\n", "    name=\"assistant\",\n", "    model_client=client,\n", "    system_message=(\n", "        \"You are a helpful AI assistant that provides answers using ONLY the provided context. \"\n", "        \"Do NOT include any external information. Base your answer entirely on the context given below.\"\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Query Processing with RAG\n", "\n", "We define the `ask_rag` function to send the query to the assistant, process the response, and evaluate it. This function handles the interaction with the assistant and uses the evaluator to measure the quality of the response."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["async def ask_rag_agent(query: str, context_provider: ContextProvider, location: str = None):\n", "    \"\"\"\n", "    Sends a query to the assistant agent with context from the provider.\n", "    \n", "    Args:\n", "        query: The user's question\n", "        context_provider: The context provider instance\n", "        location: Optional location for weather queries\n", "    \"\"\"\n", "    try:\n", "        # Get unified context\n", "        context = context_provider.get_unified_context(query, location)\n", "        \n", "        # Augment the query with context\n", "        augmented_query = (\n", "            f\"{context}\\n\\n\"\n", "            f\"User Query: {query}\\n\\n\"\n", "            \"Based ONLY on the above context, please provide a helpful answer.\"\n", "        )\n", "\n", "        # Send the augmented query to the assistant\n", "        start_time = time.time()\n", "        response = await assistant.on_messages(\n", "            [TextMessage(content=augmented_query, source=\"user\")],\n", "            cancellation_token=CancellationToken(),\n", "        )\n", "        processing_time = time.time() - start_time\n", "        \n", "        return {\n", "            'query': query,\n", "            'response': response.chat_message.content,\n", "            'processing_time': processing_time,\n", "            'location': location\n", "        }\n", "    except Exception as e:\n", "        print(f\"Error processing query: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example usage\n", "\n", "We initialize the evaluator and define the queries that we want to process and evaluate."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def main():\n", "    # Initialize context provider\n", "    context_provider = ContextProvider(collection)\n", "    \n", "    # Example queries\n", "    queries = [\n", "        {\"query\": \"What does Contoso's travel insurance cover?\"},\n", "        {\"query\": \"What's the weather like in London?\", \"location\": \"london\"},\n", "        {\"query\": \"What luxury destinations does Contoso offer and what's the weather in Paris?\", \"location\": \"paris\"},\n", "    ]\n", "    \n", "    print(\"=== Autogen RAG Demo ===\")\n", "    for query_data in queries:\n", "        query = query_data[\"query\"]\n", "        location = query_data.get(\"location\")\n", "        \n", "        print(f\"\\n\\nQuery: {query}\")\n", "        if location:\n", "            print(f\"Location: {location}\")\n", "        \n", "        # Show the context being used\n", "        context = context_provider.get_unified_context(query, location)\n", "        print(\"\\n--- Context Used ---\")\n", "        print(context)\n", "        print(\"-------------------\")\n", "        \n", "        # Get response from the agent\n", "        result = await ask_rag_agent(query, context_provider, location)\n", "        if result:\n", "            print(f\"\\nResponse: {result['response']}\")\n", "        print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Script\n", "\n", "We check if the script is running in an interactive environment or a standard script, and run the main function accordingly."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Autogen RAG Demo ===\n", "\n", "\n", "Query: What does Contoso's travel insurance cover?\n", "\n", "--- Context Used ---\n", "Retrieved Context:\n", "Document: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "\n", "-------------------\n", "\n", "Response: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "\n", "==================================================\n", "\n", "\n", "Query: What's the weather like in London?\n", "Location: london\n", "\n", "--- Context Used ---\n", "Retrieved Context:\n", "Document: Popular destinations include the Maldives, Swiss Alps, and African safaris.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "\n", "Weather Information for london:\n", "Weather for London:\n", "Temperature: 60°F\n", "Condition: <PERSON><PERSON>\n", "Humidity: 80%\n", "Wind: 15 mph\n", "-------------------\n", "\n", "Response: The weather in London is rainy with a temperature of 60°F, 80% humidity, and winds at 15 mph.\n", "\n", "==================================================\n", "\n", "\n", "Query: What luxury destinations does Contoso offer and what's the weather in Paris?\n", "Location: paris\n", "\n", "--- Context Used ---\n", "Retrieved Context:\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "Document: Contoso Travel provides exclusive access to boutique hotels and private guided tours.\n", "Metadata: {'source': 'training', 'type': 'explanation'}\n", "\n", "\n", "Weather Information for paris:\n", "Weather for Paris:\n", "Temperature: 68°F\n", "Condition: Cloudy\n", "Humidity: 70%\n", "Wind: 8 mph\n", "-------------------\n", "\n", "Response: Contoso Travel offers luxury vacation packages to exotic destinations worldwide and provides exclusive access to boutique hotels and private guided tours. The weather in Paris is cloudy with a temperature of 68°F, 70% humidity, and winds at 8 mph.\n", "\n", "==================================================\n"]}], "source": ["if __name__ == \"__main__\":\n", "    if asyncio.get_event_loop().is_running():\n", "        await main()\n", "    else:\n", "        asyncio.run(main())"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}