{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Semantic Kernel Tool Use Example\n", "\n", "This document provides an overview and explanation of the code used to create a Semantic Kernel-based tool that integrates with Azure AI Search for Retrieval-Augmented Generation (RAG). The example demonstrates how to build an AI agent that retrieves travel documents from an Azure AI Search index, augments user queries with semantic search results, and streams detailed travel recommendations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initializing the Environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing Packages\n", "The following code imports the necessary packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from typing import Annotated\n", "\n", "from IPython.display import display, HTML\n", "\n", "from dotenv import load_dotenv\n", "\n", "from azure.core.credentials import AzureKeyCredential\n", "from azure.search.documents import SearchClient\n", "from azure.search.documents.indexes import SearchIndexClient\n", "from azure.search.documents.indexes.models import SearchIndex, SimpleField, SearchFieldDataType, SearchableField\n", "\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import FunctionCallContent,FunctionResultContent, StreamingTextContent\n", "from semantic_kernel.functions import kernel_function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating the Semantic Kernel and AI Service\n", "\n", "A Semantic Kernel instance is created and configured with an asynchronous OpenAI chat completion service. The service is added to the kernel for use in generating responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "# Initialize the asynchronous OpenAI client\n", "client = AsyncOpenAI(\n", "    api_key=os.environ[\"GITHUB_TOKEN\"],\n", "    base_url=\"https://models.inference.ai.azure.com/\"\n", ")\n", "\n", "# Create the OpenAI Chat Completion Service\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining the Prompt Plugin\n", "\n", "The PromptPlugin is a native plugin that defines a function to build an augmented prompt using retrieval context"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SearchPlugin:\n", "\n", "    def __init__(self, search_client: SearchClient):\n", "        self.search_client = search_client\n", "\n", "    @kernel_function(\n", "        name=\"build_augmented_prompt\",\n", "        description=\"Build an augmented prompt using retrieval context or function results.\",\n", "    )\n", "    def build_augmented_prompt(self, query: str, retrieval_context: str) -> str:\n", "        return (\n", "            f\"Retrieved Context:\\n{retrieval_context}\\n\\n\"\n", "            f\"User Query: {query}\\n\\n\"\n", "            \"First review the retrieved context, if this does not answer the query, try calling an available plugin functions that might give you an answer. If no context is available, say so.\"\n", "        )\n", "    \n", "    @kernel_function(\n", "        name=\"retrieve_documents\",\n", "        description=\"Retrieve documents from the Azure Search service.\",\n", "    )\n", "    def get_retrieval_context(self, query: str) -> str:\n", "        results = self.search_client.search(query)\n", "        context_strings = []\n", "        for result in results:\n", "            context_strings.append(f\"Document: {result['content']}\")\n", "        return \"\\n\\n\".join(context_strings) if context_strings else \"No results found\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class WeatherInfoPlugin:\n", "    \"\"\"A Plugin that provides the average temperature for a travel destination.\"\"\"\n", "\n", "    def __init__(self):\n", "        # Dictionary of destinations and their average temperatures\n", "        self.destination_temperatures = {\n", "            \"maldives\": \"82°F (28°C)\",\n", "            \"swiss alps\": \"45°F (7°C)\",\n", "            \"african safaris\": \"75°F (24°C)\"\n", "        }\n", "\n", "    @kernel_function(description=\"Get the average temperature for a specific travel destination.\")\n", "    def get_destination_temperature(self, destination: str) -> Annotated[str, \"Returns the average temperature for the destination.\"]:\n", "        \"\"\"Get the average temperature for a travel destination.\"\"\"\n", "        # Normalize the input destination (lowercase)\n", "        normalized_destination = destination.lower()\n", "\n", "        # Look up the temperature for the destination\n", "        if normalized_destination in self.destination_temperatures:\n", "            return f\"The average temperature in {destination} is {self.destination_temperatures[normalized_destination]}.\"\n", "        else:\n", "            return f\"Sorry, I don't have temperature information for {destination}. Available destinations are: Maldives, Swiss Alps, and African safaris.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vector Database Initialization\n", "\n", "We initialize Azure AI Search with persistent storage and add enhanced sample documents. Azure AI Search will be used to store and retrieve documents that provide context for generating accurate responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize Azure AI Search with persistent storage\n", "search_service_endpoint = os.getenv(\"AZURE_SEARCH_SERVICE_ENDPOINT\")\n", "search_api_key = os.getenv(\"AZURE_SEARCH_API_KEY\")\n", "index_name = \"travel-documents\"\n", "\n", "search_client = SearchClient(\n", "    endpoint=search_service_endpoint,\n", "    index_name=index_name,\n", "    credential=AzureKeyCredential(search_api_key)\n", ")\n", "\n", "index_client = SearchIndexClient(\n", "    endpoint=search_service_endpoint,\n", "    credential=AzureKeyCredential(search_api_key)\n", ")\n", "\n", "# Define the index schema\n", "fields = [\n", "    SimpleField(name=\"id\", type=SearchFieldDataType.String, key=True),\n", "    SearchableField(name=\"content\", type=SearchFieldDataType.String)\n", "]\n", "\n", "index = SearchIndex(name=index_name, fields=fields)\n", "\n", "# Check if index already exists if not, create it\n", "try:\n", "    existing_index = index_client.get_index(index_name)\n", "    print(f\"Index '{index_name}' already exists, using the existing index.\")\n", "except Exception:\n", "    # Create the index if it doesn't exist\n", "    print(f\"Creating new index '{index_name}'...\")\n", "    index_client.create_index(index)\n", "\n", "\n", "# Enhanced sample documents\n", "documents = [\n", "    {\"id\": \"1\", \"content\": \"Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\"},\n", "    {\"id\": \"2\", \"content\": \"Our premium travel services include personalized itinerary planning and 24/7 concierge support.\"},\n", "    {\"id\": \"3\", \"content\": \"Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\"},\n", "    {\"id\": \"4\", \"content\": \"Popular destinations include the Maldives, Swiss Alps, and African safaris.\"},\n", "    {\"id\": \"5\", \"content\": \"Contoso Travel provides exclusive access to boutique hotels and private guided tours.\"}\n", "]\n", "\n", "# Add documents to the index\n", "search_client.upload_documents(documents)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    plugins=[SearchPlugin(search_client=search_client), WeatherInfoPlugin()],\n", "    name=\"TravelAgent\",\n", "    instructions=\"Answer travel queries using the provided tools and context. If context is provided, do not say 'I have no context for that.'\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running the Agent with Streaming Invocation\n", "\n", "The main asynchronous loop creates a thread for the conversation and, for each user input,  so that the agent sees the retrieval context. The user message is also added, and then the agent is invoked using streaming. The output is printed as it streams in."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def main():\n", "    thread: ChatHistoryAgentThread | None = None\n", "\n", "    user_inputs = [\n", "        \"Can you explain Contoso's travel insurance coverage?\",\n", "        \"What is the average temperature of the Maldives?\",\n", "        \"What is a good cold destination offered by Contoso and what is it average temperature?\",\n", "    ]\n", "\n", "    for user_input in user_inputs:\n", "        html_output = (\n", "            f\"<div style='margin-bottom:10px'>\"\n", "            f\"<div style='font-weight:bold'>User:</div>\"\n", "            f\"<div style='margin-left:20px'>{user_input}</div></div>\"\n", "        )\n", "\n", "        agent_name = None\n", "        full_response: list[str] = []\n", "        function_calls: list[str] = []\n", "\n", "        # Buffer to reconstruct streaming function call\n", "        current_function_name = None\n", "        argument_buffer = \"\"\n", "\n", "        async for response in agent.invoke_stream(\n", "            messages=user_input,\n", "            thread=thread,\n", "        ):\n", "            thread = response.thread\n", "            agent_name = response.name\n", "            content_items = list(response.items)\n", "\n", "            for item in content_items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    if item.function_name:\n", "                        current_function_name = item.function_name\n", "\n", "                    # Accumulate arguments (streamed in chunks)\n", "                    if isinstance(item.arguments, str):\n", "                        argument_buffer += item.arguments\n", "                elif isinstance(item, FunctionResultContent):\n", "                    # Finalize any pending function call before showing result\n", "                    if current_function_name:\n", "                        formatted_args = argument_buffer.strip()\n", "                        try:\n", "                            parsed_args = json.loads(formatted_args)\n", "                            formatted_args = json.dumps(parsed_args)\n", "                        except Exception:\n", "                            pass  # leave as raw string\n", "\n", "                        function_calls.append(f\"Calling function: {current_function_name}({formatted_args})\")\n", "                        current_function_name = None\n", "                        argument_buffer = \"\"\n", "\n", "                    function_calls.append(f\"\\nFunction Result:\\n\\n{item.result}\")\n", "                elif isinstance(item, StreamingTextContent) and item.text:\n", "                    full_response.append(item.text)\n", "\n", "        if function_calls:\n", "            html_output += (\n", "                \"<div style='margin-bottom:10px'>\"\n", "                \"<details>\"\n", "                \"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"\n", "                \"<div style='margin:10px; padding:10px; background-color:#f8f8f8; \"\n", "                \"border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"\n", "                f\"{chr(10).join(function_calls)}\"\n", "                \"</div></details></div>\"\n", "            )\n", "\n", "        html_output += (\n", "            \"<div style='margin-bottom:20px'>\"\n", "            f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"\n", "            f\"<div style='margin-left:20px; white-space:pre-wrap'>{''.join(full_response)}</div></div><hr>\"\n", "        )\n", "\n", "        display(HTML(html_output))\n", "\n", "await main()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You should see output similar to the following:\n", "\n", "```text\n", "User: \n", "Can you explain Contoso's travel insurance coverage?\n", "\n", "Function Calls (click to expand)\n", "\n", "Calling function: retrieve_documents({\"query\": \"Contoso travel insurance coverage\"})\n", "\n", "Function Result:\n", "\n", "Document: Contoso's travel insurance covers medical emergencies, trip cancellations, and lost baggage.\n", "\n", "Document: Contoso Travel offers luxury vacation packages to exotic destinations worldwide.\n", "\n", "Document: Contoso Travel provides exclusive access to boutique hotels and private guided tours.\n", "\n", "Document: Our premium travel services include personalized itinerary planning and 24/7 concierge support.\n", "\n", "TravelAgent:\n", "\n", "Contoso's travel insurance coverage includes the following:\n", "\n", "1. **Medical Emergencies**: Coverage for unforeseen medical issues that may arise while traveling.\n", "2. **Trip Cancellations**: Protection in case you need to cancel your trip for covered reasons.\n", "3. **Lost <PERSON>age**: Compensation for baggage that is lost during your trip.\n", "\n", "If you need more specific details about the policy, it would be best to contact Contoso directly or refer to their official documentation.\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}