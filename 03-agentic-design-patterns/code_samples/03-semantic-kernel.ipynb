{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON> \n", "\n", "In this code sample, you will use the [Semantic Kernel](https://aka.ms/ai-agents-beginners/semantic-kernel) AI Framework to create a basic agent. \n", "\n", "The goal of this sample is to show you the steps that we will later use in the additional code samples when implementing the different agentic patterns. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import the Needed Python Packages "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from typing import Annotated\n", "\n", "from dotenv import load_dotenv\n", "\n", "from IPython.display import display, HTML\n", "\n", "from openai import AsyncOpenAI\n", "\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import FunctionCallContent, FunctionResultContent, StreamingTextContent\n", "from semantic_kernel.functions import kernel_function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Client\n", "\n", "In this sample, we will use [GitHub Models](https://aka.ms/ai-agents-beginners/github-models) for access to the LLM. \n", "\n", "The `ai_model_id` is defined as `gpt-4o-mini`. Try changing the model to another model available on the GitHub Models marketplace to see the different results. \n", "\n", "For us to use the `Azure Inference SDK` that is used for the `base_url` for GitHub Models, we will use the `OpenAIChatCompletion` connector within Semantic Kernel. There are also other [available connectors](https://learn.microsoft.com/semantic-kernel/concepts/ai-services/chat-completion) to use Semantic Kernel for other model providers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random   \n", "\n", "# Define a sample plugin for the sample\n", "\n", "class DestinationsPlugin:\n", "    \"\"\"A List of Random Destinations for a vacation.\"\"\"\n", "\n", "    def __init__(self):\n", "        # List of vacation destinations\n", "        self.destinations = [\n", "            \"Barcelona, Spain\",\n", "            \"Paris, France\",\n", "            \"Berlin, Germany\",\n", "            \"Tokyo, Japan\",\n", "            \"Sydney, Australia\",\n", "            \"New York, USA\",\n", "            \"Cairo, Egypt\",\n", "            \"Cape Town, South Africa\",\n", "            \"Rio de Janeiro, Brazil\",\n", "            \"Bali, Indonesia\"\n", "        ]\n", "        # Track last destination to avoid repeats\n", "        self.last_destination = None\n", "\n", "    @kernel_function(description=\"Provides a random vacation destination.\")\n", "    def get_random_destination(self) -> Annotated[str, \"Returns a random vacation destination.\"]:\n", "        # Get available destinations (excluding last one if possible)\n", "        available_destinations = self.destinations.copy()\n", "        if self.last_destination and len(available_destinations) > 1:\n", "            available_destinations.remove(self.last_destination)\n", "\n", "        # Select a random destination\n", "        destination = random.choice(available_destinations)\n", "\n", "        # Update the last destination\n", "        self.last_destination = destination\n", "\n", "        return destination"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "client = AsyncOpenAI(\n", "    api_key=os.environ.get(\"GITHUB_TOKEN\"), \n", "    base_url=\"https://models.inference.ai.azure.com/\",\n", ")\n", "\n", "# Create an AI Service that will be used by the `ChatCompletionAgent`\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"gpt-4o-mini\",\n", "    async_client=client,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Agent \n", "\n", "Below we create the Agent called `TravelAgent`.\n", "\n", "For this example, we are using very simple instructions. You can change these instructions to see how the agent responds differently. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AGENT_INSTRUCTIONS = \"\"\"You are a helpful AI Agent that can help plan vacations for customers.\n", "\n", "Important: When users specify a destination, always plan for that location. Only suggest random destinations when the user hasn't specified a preference.\n", "\n", "When the conversation begins, introduce yourself with this message:\n", "\"Hello! I'm your TravelAgent assistant. I can help plan vacations and suggest interesting destinations for you. Here are some things you can ask me:\n", "1. Plan a day trip to a specific location\n", "2. Suggest a random vacation destination\n", "3. Find destinations with specific features (beaches, mountains, historical sites, etc.)\n", "4. Plan an alternative trip if you don't like my first suggestion\n", "\n", "What kind of trip would you like me to help you plan today?\"\n", "\n", "Always prioritize user preferences. If they mention a specific destination like \"Bali\" or \"Paris,\" focus your planning on that location rather than suggesting alternatives.\n", "\"\"\"\n", "\n", "agent = ChatCompletionAgent(\n", "    service=chat_completion_service, \n", "    plugins=[DestinationsPlugin()],\n", "    name=\"TravelAgent\",\n", "    instructions=AGENT_INSTRUCTIONS,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the Agents \n", "\n", "Now we can run the Agent by defining the `ChatHistory` and adding the `system_message` to it. We will use the `AGENT_INSTRUCTIONS` that we defined earlier. \n", "\n", "After these are defined, we create a `user_inputs` that will be what the user is sending to the agent. In this case, we have set this message to `Plan me a sunny vacation`. \n", "\n", "Feel free to change this message to see how the agent responds differently. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_inputs = [\n", "    \"Plan me a day trip.\",\n", "    \"I don't like that destination. Plan me another vacation.\",\n", "]\n", "\n", "async def main():\n", "    thread: ChatHistoryAgentThread | None = None\n", "\n", "    for user_input in user_inputs:\n", "        html_output = (\n", "            f\"<div style='margin-bottom:10px'>\"\n", "            f\"<div style='font-weight:bold'>User:</div>\"\n", "            f\"<div style='margin-left:20px'>{user_input}</div></div>\"\n", "        )\n", "\n", "        agent_name = None\n", "        full_response: list[str] = []\n", "        function_calls: list[str] = []\n", "\n", "        # Buffer to reconstruct streaming function call\n", "        current_function_name = None\n", "        argument_buffer = \"\"\n", "\n", "        async for response in agent.invoke_stream(\n", "            messages=user_input,\n", "            thread=thread,\n", "        ):\n", "            thread = response.thread\n", "            agent_name = response.name\n", "            content_items = list(response.items)\n", "\n", "            for item in content_items:\n", "                if isinstance(item, FunctionCallContent):\n", "                    if item.function_name:\n", "                        current_function_name = item.function_name\n", "\n", "                    # Accumulate arguments (streamed in chunks)\n", "                    if isinstance(item.arguments, str):\n", "                        argument_buffer += item.arguments\n", "                elif isinstance(item, FunctionResultContent):\n", "                    # Finalize any pending function call before showing result\n", "                    if current_function_name:\n", "                        formatted_args = argument_buffer.strip()\n", "                        try:\n", "                            parsed_args = json.loads(formatted_args)\n", "                            formatted_args = json.dumps(parsed_args)\n", "                        except Exception:\n", "                            pass  # leave as raw string\n", "\n", "                        function_calls.append(f\"Calling function: {current_function_name}({formatted_args})\")\n", "                        current_function_name = None\n", "                        argument_buffer = \"\"\n", "\n", "                    function_calls.append(f\"\\nFunction Result:\\n\\n{item.result}\")\n", "                elif isinstance(item, StreamingTextContent) and item.text:\n", "                    full_response.append(item.text)\n", "\n", "        if function_calls:\n", "            html_output += (\n", "                \"<div style='margin-bottom:10px'>\"\n", "                \"<details>\"\n", "                \"<summary style='cursor:pointer; font-weight:bold; color:#0066cc;'>Function Calls (click to expand)</summary>\"\n", "                \"<div style='margin:10px; padding:10px; background-color:#f8f8f8; \"\n", "                \"border:1px solid #ddd; border-radius:4px; white-space:pre-wrap; font-size:14px; color:#333;'>\"\n", "                f\"{chr(10).join(function_calls)}\"\n", "                \"</div></details></div>\"\n", "            )\n", "\n", "        html_output += (\n", "            \"<div style='margin-bottom:20px'>\"\n", "            f\"<div style='font-weight:bold'>{agent_name or 'Assistant'}:</div>\"\n", "            f\"<div style='margin-left:20px; white-space:pre-wrap'>{''.join(full_response)}</div></div><hr>\"\n", "        )\n", "\n", "        display(HTML(html_output))\n", "\n", "await main()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}